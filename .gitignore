# =============================================================================
# Database Explorer MCP - Git Ignore Configuration
# =============================================================================
# This file specifies intentionally untracked files that Git should ignore.
# See: https://git-scm.com/docs/gitignore
# =============================================================================

# =============================================================================
# Python Environment & Cache
# =============================================================================

# Virtual environments
venv/
.venv/
env/
.env/
ENV/
env.bak/
venv.bak/

# Python cache files
__pycache__/
*.py[cod]
*$py.class
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# =============================================================================
# Environment Variables & Configuration
# =============================================================================

# Environment files with real credentials (keep .env.example)
.env
.env.local
.env.production
.env.staging
.env.development
config.ini
config.cfg
secrets.json
credentials.json

# Database connection files
database.ini
db_config.ini
connection_string.txt

# =============================================================================
# Logs & Temporary Files
# =============================================================================

# Log files and directories
logs/
*.log
*.log.*
log_*.txt
debug.log
error.log
access.log

# Temporary files
*.tmp
*.temp
*.bak
*.backup
*~
.#*
#*#

# =============================================================================
# Project-Specific Files
# =============================================================================

# Cleanup directory (created by cleanup_project.bat)
to_delete/

# Table analysis files (generated content)
*_analysis.md
ffmaprep_analysis.md
supermag_*.md
table_analysis_*.md
schema_analysis_*.md

# Test files (except main test file)
my_test.py
test_connection_*.py
test_drcp_*.py
test_oracle_*.py
query_*.py
!test_mcp_server.py

# Old configuration and prompt files
old_*.md
*_prompt.md
oracle_connection_prompt.md

# Generated documentation (keep manual docs)
auto_generated_*.md
generated_docs/

# Export files (keep requirements files)
exports/
*.xlsx
*.csv
!requirements*.txt

# =============================================================================
# IDE & Editor Files
# =============================================================================

# Visual Studio Code
.vscode/
.history/
*.code-workspace

# PyCharm / IntelliJ
.idea/
*.iws
*.iml
*.ipr

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo
*~
.netrwhist

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# Atom
.atom/

# =============================================================================
# Operating System Files
# =============================================================================

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.stackdump
[Dd]esktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# =============================================================================
# Development Tools
# =============================================================================

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
Pipfile.lock

# Poetry
poetry.lock

# Celery
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# =============================================================================
# Database & MCP Specific
# =============================================================================

# Database files
*.db
*.sqlite
*.sqlite3
*.db3

# MCP generated files
mcp_cache/
mcp_logs/
.mcp/

# Oracle specific
*.ora
tnsnames.ora
sqlnet.ora

# Connection test files
connection_test_*.py
db_test_*.py

# =============================================================================
# Security & Sensitive Data
# =============================================================================

# SSH keys
*.pem
*.key
*.crt
*.p12
*.pfx

# API keys and tokens
api_keys.txt
tokens.json
.secrets

# =============================================================================
# Build & Deployment
# =============================================================================

# Docker
.dockerignore
Dockerfile.local
docker-compose.override.yml

# Terraform
*.tfstate
*.tfstate.*
.terraform/
.terraform.lock.hcl

# =============================================================================
# Documentation Build
# =============================================================================

# Sphinx documentation
docs/_build/
docs/build/

# LaTeX
*.aux
*.bbl
*.blg
*.fdb_latexmk
*.fls
*.idx
*.ilg
*.ind
*.lof
*.lot
*.out
*.toc
*.synctex.gz

# =============================================================================
# Specific Files to Ignore
# =============================================================================

# Temporary image files
photo_*.jpg
photo_*.png
screenshot_*.png

# Additional requirements files (keep main ones)
_requirements.txt
requirements_db_explorer.txt

# =============================================================================
# END OF .gitignore
# =============================================================================
