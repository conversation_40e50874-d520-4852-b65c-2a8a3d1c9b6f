#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Универсальный установщик Database Explorer MCP для различных ИИ-редакторов.
"""

import json
import os
import platform
import subprocess
import sys
from pathlib import Path
from typing import Dict, Any, Optional


class MCPInstaller:
    """Универсальный установщик MCP для различных редакторов."""
    
    def __init__(self):
        self.current_dir = Path(__file__).parent.parent.absolute()
        self.mcp_server_path = self.current_dir / "mcp_server.py"
        
    def detect_editors(self) -> Dict[str, bool]:
        """Определяет установленные ИИ-редакторы."""
        editors = {
            "Claude Desktop": self._check_claude_desktop(),
            "Cursor IDE": self._check_cursor(),
            "Augment Code": self._check_augment(),
            "VS Code": self._check_vscode(),
            "Windsurf": self._check_windsurf(),
            "Continue": self._check_continue()
        }
        return editors
    
    def _check_claude_desktop(self) -> bool:
        """Проверяет наличие Claude Desktop."""
        system = platform.system()
        if system == "Windows":
            appdata = os.getenv("APPDATA")
            return Path(appdata, "Claude").exists() if appdata else False
        elif system == "Darwin":
            return Path.home().joinpath("Library/Application Support/Claude").exists()
        elif system == "Linux":
            return Path.home().joinpath(".config/Claude").exists()
        return False
    
    def _check_cursor(self) -> bool:
        """Проверяет наличие Cursor IDE."""
        try:
            result = subprocess.run(["cursor", "--version"], 
                                  capture_output=True, text=True, timeout=5)
            return result.returncode == 0
        except:
            return False
    
    def _check_augment(self) -> bool:
        """Проверяет наличие Augment Code."""
        system = platform.system()
        if system == "Windows":
            appdata = os.getenv("APPDATA")
            return Path(appdata, "Augment").exists() if appdata else False
        elif system == "Darwin":
            return Path.home().joinpath("Library/Application Support/Augment").exists()
        elif system == "Linux":
            return Path.home().joinpath(".config/Augment").exists()
        return False
    
    def _check_vscode(self) -> bool:
        """Проверяет наличие VS Code."""
        try:
            result = subprocess.run(["code", "--version"], 
                                  capture_output=True, text=True, timeout=5)
            return result.returncode == 0
        except:
            return False
    
    def _check_windsurf(self) -> bool:
        """Проверяет наличие Windsurf."""
        try:
            result = subprocess.run(["windsurf", "--version"], 
                                  capture_output=True, text=True, timeout=5)
            return result.returncode == 0
        except:
            return False
    
    def _check_continue(self) -> bool:
        """Проверяет наличие Continue расширения."""
        system = platform.system()
        if system == "Windows":
            appdata = os.getenv("APPDATA")
            vscode_path = Path(appdata, "Code/User/globalStorage")
        elif system == "Darwin":
            vscode_path = Path.home().joinpath("Library/Application Support/Code/User/globalStorage")
        elif system == "Linux":
            vscode_path = Path.home().joinpath(".config/Code/User/globalStorage")
        else:
            return False
        
        if not vscode_path.exists():
            return False
        
        # Ищем Continue расширение
        for item in vscode_path.iterdir():
            if "continue" in item.name.lower():
                return True
        return False
    
    def install_for_claude_desktop(self) -> bool:
        """Устанавливает для Claude Desktop."""
        try:
            from install_mcp import install_mcp_server
            return install_mcp_server()
        except ImportError:
            print("❌ Модуль install_mcp не найден")
            return False
    
    def install_for_cursor(self) -> bool:
        """Устанавливает для Cursor IDE."""
        try:
            sys.path.append(str(Path(__file__).parent / "cursor"))
            from install_cursor import install_mcp_for_cursor
            return install_mcp_for_cursor()
        except ImportError:
            print("❌ Модуль install_cursor не найден")
            return False
    
    def install_for_augment(self) -> bool:
        """Устанавливает для Augment Code."""
        try:
            sys.path.append(str(Path(__file__).parent / "augment"))
            from install_augment import install_mcp_for_augment
            return install_mcp_for_augment()
        except ImportError:
            print("❌ Модуль install_augment не найден")
            return False
    
    def install_for_vscode(self) -> bool:
        """Устанавливает для VS Code с Continue."""
        print("📝 Для VS Code с Continue требуется ручная настройка:")
        print("1. Установите расширение Continue")
        print("2. Добавьте в config.json Continue:")
        
        config = {
            "mcpServers": {
                "database-explorer": {
                    "command": "python",
                    "args": [str(self.mcp_server_path)],
                    "env": {
                        "DB_TYPE": "oracle"
                    }
                }
            }
        }
        
        print(json.dumps(config, indent=2))
        return True
    
    def create_universal_config(self) -> Dict[str, Any]:
        """Создает универсальную конфигурацию MCP."""
        return {
            "mcpServers": {
                "database-explorer": {
                    "name": "Database Explorer",
                    "description": "Исследование структуры реляционных баз данных",
                    "command": "python",
                    "args": [str(self.mcp_server_path)],
                    "env": {
                        "DB_TYPE": os.getenv("DB_TYPE", "oracle"),
                        "DB_HOST": os.getenv("DB_HOST", ""),
                        "DB_PORT": os.getenv("DB_PORT", "1521"),
                        "DB_USER": os.getenv("DB_USER", ""),
                        "DB_PASSWORD": os.getenv("DB_PASSWORD", ""),
                        "DB_SERVICE": os.getenv("DB_SERVICE", ""),
                        "DB_NAME": os.getenv("DB_NAME", "")
                    },
                    "capabilities": {
                        "tools": True,
                        "resources": False,
                        "prompts": False
                    },
                    "settings": {
                        "autoStart": True,
                        "logLevel": "info",
                        "timeout": 30000
                    }
                }
            }
        }
    
    def run_interactive_installer(self):
        """Запускает интерактивный установщик."""
        print("🚀 Database Explorer MCP - Универсальный установщик")
        print("=" * 60)
        
        # Проверяем MCP сервер
        if not self.mcp_server_path.exists():
            print(f"❌ MCP сервер не найден: {self.mcp_server_path}")
            return False
        
        print("✅ MCP сервер найден")
        
        # Определяем доступные редакторы
        print("\n🔍 Поиск установленных ИИ-редакторов...")
        editors = self.detect_editors()
        
        available_editors = [name for name, available in editors.items() if available]
        
        if not available_editors:
            print("❌ Не найдено поддерживаемых ИИ-редакторов")
            print("💡 Убедитесь, что установлен один из:")
            print("   - Claude Desktop")
            print("   - Cursor IDE") 
            print("   - Augment Code")
            print("   - VS Code с Continue")
            return False
        
        print("✅ Найдены редакторы:")
        for i, editor in enumerate(available_editors, 1):
            print(f"   {i}. {editor}")
        
        # Выбор редактора
        print(f"\nВыберите редактор для установки (1-{len(available_editors)}):")
        try:
            choice = int(input("Введите номер: ")) - 1
            if 0 <= choice < len(available_editors):
                selected_editor = available_editors[choice]
            else:
                print("❌ Неверный выбор")
                return False
        except ValueError:
            print("❌ Неверный ввод")
            return False
        
        # Установка для выбранного редактора
        print(f"\n🔧 Установка для {selected_editor}...")
        
        success = False
        if selected_editor == "Claude Desktop":
            success = self.install_for_claude_desktop()
        elif selected_editor == "Cursor IDE":
            success = self.install_for_cursor()
        elif selected_editor == "Augment Code":
            success = self.install_for_augment()
        elif selected_editor == "VS Code":
            success = self.install_for_vscode()
        
        if success:
            print(f"✅ Установка для {selected_editor} завершена!")
            print("\n📋 Следующие шаги:")
            print("1. Перезапустите редактор")
            print("2. Убедитесь, что настроены переменные окружения (.env файл)")
            print("3. Протестируйте подключение")
        else:
            print(f"❌ Ошибка при установке для {selected_editor}")
        
        return success


def main():
    """Главная функция."""
    installer = MCPInstaller()
    installer.run_interactive_installer()


if __name__ == "__main__":
    main()
