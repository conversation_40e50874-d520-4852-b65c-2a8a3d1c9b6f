#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Скрипт для получения информации о таблице FFMAPREP.
"""

import os
import sys
from loguru import logger
from sqlalchemy import text
from tabulate import tabulate

# Настройка логирования
logger.add(
    "logs/ffmaprep_analysis.log",
    rotation="100 MB",
    retention="30 days",
    format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {function}:{line} | {message}",
    level="INFO",
)


def execute_select_query(query, params=None):
    """
    Выполнение SELECT-запроса с обработкой ошибок.
    """
    try:
        # Импортируем функцию создания сессии
        from config_db import create_session

        # Создаем новую сессию для каждого запроса
        with create_session() as session:
            result = session.execute(text(query), params or {})
            return result.fetchall()
    except Exception as e:
        logger.error(f"Ошибка при выполнении SELECT-запроса: {str(e)}")
        logger.error(f"Запрос: {query}")
        logger.error(f"Параметры: {params}")
        return []


def get_table_structure():
    """Получение структуры таблицы FFMAPREP."""
    query = """
    SELECT column_name, data_type, data_length, nullable
    FROM all_tab_columns
    WHERE table_name = 'FFMAPREP'
    AND owner = 'SUPERMAG'
    ORDER BY column_id
    """
    result = execute_select_query(query)

    # Преобразование результата в список словарей
    columns_info = []
    for row in result:
        if isinstance(row, tuple):
            columns_info.append(
                {
                    "column_name": row[0],
                    "data_type": row[1],
                    "data_length": row[2],
                    "nullable": row[3],
                }
            )
        else:
            columns_info.append(
                {
                    "column_name": row.column_name,
                    "data_type": row.data_type,
                    "data_length": row.data_length,
                    "nullable": row.nullable,
                }
            )

    return columns_info


def get_table_constraints():
    """Получение ограничений таблицы FFMAPREP."""
    query = """
    SELECT c.constraint_name, c.constraint_type, cc.column_name
    FROM all_constraints c
    JOIN all_cons_columns cc ON c.constraint_name = cc.constraint_name
    WHERE c.table_name = 'FFMAPREP'
    AND c.owner = 'SUPERMAG'
    ORDER BY c.constraint_type, cc.position
    """
    result = execute_select_query(query)

    # Преобразование результата в список словарей
    constraints_info = []
    for row in result:
        if isinstance(row, tuple):
            constraints_info.append(
                {
                    "constraint_name": row[0],
                    "constraint_type": row[1],
                    "column_name": row[2],
                }
            )
        else:
            constraints_info.append(
                {
                    "constraint_name": row.constraint_name,
                    "constraint_type": row.constraint_type,
                    "column_name": row.column_name,
                }
            )

    return constraints_info


def get_table_relationships():
    """Получение связей таблицы FFMAPREP с другими таблицами."""
    query = """
    -- Получение внешних ключей, где таблица ссылается на другие
    SELECT a.table_name as referenced_table,
           a.constraint_name as fk_constraint,
           acc.column_name as fk_column,
           r.table_name as referenced_by_table,
           rc.column_name as referenced_column,
           'outgoing' as dependency_type
    FROM all_constraints a
    JOIN all_cons_columns acc ON a.constraint_name = acc.constraint_name AND a.owner = acc.owner
    JOIN all_constraints r ON a.r_constraint_name = r.constraint_name
    JOIN all_cons_columns rc ON r.constraint_name = rc.constraint_name AND r.owner = rc.owner
    WHERE a.constraint_type = 'R'
    AND a.table_name = 'FFMAPREP'
    AND a.owner = 'SUPERMAG'

    UNION ALL

    -- Получение внешних ключей, где на таблицу ссылаются другие
    SELECT a.table_name as referenced_table,
           a.constraint_name as fk_constraint,
           acc.column_name as fk_column,
           r.table_name as referenced_by_table,
           rc.column_name as referenced_column,
           'incoming' as dependency_type
    FROM all_constraints a
    JOIN all_cons_columns acc ON a.constraint_name = acc.constraint_name AND a.owner = acc.owner
    JOIN all_constraints r ON a.r_constraint_name = r.constraint_name
    JOIN all_cons_columns rc ON r.constraint_name = rc.constraint_name AND r.owner = rc.owner
    WHERE a.constraint_type = 'R'
    AND r.table_name = 'FFMAPREP'
    AND r.owner = 'SUPERMAG'

    ORDER BY dependency_type, referenced_table
    """
    result = execute_select_query(query)

    # Преобразование результата в список словарей
    relationships_info = []
    for row in result:
        if isinstance(row, tuple):
            relationships_info.append(
                {
                    "referenced_table": row[0],
                    "fk_constraint": row[1],
                    "fk_column": row[2],
                    "referenced_by_table": row[3],
                    "referenced_column": row[4],
                    "dependency_type": row[5],
                }
            )
        else:
            relationships_info.append(
                {
                    "referenced_table": row.referenced_table,
                    "fk_constraint": row.fk_constraint,
                    "fk_column": row.fk_column,
                    "referenced_by_table": row.referenced_by_table,
                    "referenced_column": row.referenced_column,
                    "dependency_type": row.dependency_type,
                }
            )

    return relationships_info


def get_table_data_sample(rectype=1):
    """Получение образца данных из таблицы FFMAPREP с RECTYPE = 1."""
    query = """
    SELECT * FROM SUPERMAG.FFMAPREP
    WHERE RECTYPE = :rectype
    AND ROWNUM <= 10
    """
    result = execute_select_query(query, {"rectype": rectype})
    return result


def get_table_statistics(rectype=1):
    """Получение статистики по данным таблицы FFMAPREP с RECTYPE = 1."""
    query = """
    SELECT
        COUNT(*) as total_records,
        MIN(SALEDATE) as min_date,
        MAX(SALEDATE) as max_date,
        SUM(SALEQ) as total_quantity,
        SUM(SALESUM) as total_sum,
        AVG(SALESUM) as avg_sum,
        COUNT(DISTINCT ARTICLE) as unique_articles,
        COUNT(DISTINCT SALEID) as unique_sales
    FROM SUPERMAG.FFMAPREP
    WHERE RECTYPE = :rectype
    """
    result = execute_select_query(query, {"rectype": rectype})

    # Преобразование результата в словарь
    if result and len(result) > 0:
        row = result[0]
        if isinstance(row, tuple):
            return {
                "total_records": row[0],
                "min_date": row[1],
                "max_date": row[2],
                "total_quantity": row[3],
                "total_sum": row[4],
                "avg_sum": row[5],
                "unique_articles": row[6],
                "unique_sales": row[7],
            }
        else:
            return {
                "total_records": row.total_records,
                "min_date": row.min_date,
                "max_date": row.max_date,
                "total_quantity": row.total_quantity,
                "total_sum": row.total_sum,
                "avg_sum": row.avg_sum,
                "unique_articles": row.unique_articles,
                "unique_sales": row.unique_sales,
            }
    return None


def create_markdown_report(
    columns_info, constraints_info, relationships_info, stats, data_sample
):
    """Создание markdown-файла с результатами анализа."""
    try:
        with open("ffmaprep_analysis.md", "w", encoding="utf-8") as f:
            f.write("# Анализ таблицы FFMAPREP\n\n")

            # Описание таблицы
            f.write("## Описание таблицы\n\n")
            f.write(
                "Таблица FFMAPREP содержит данные о продажах товаров. Особый интерес представляют записи с RECTYPE = 1, "
            )
            f.write(
                "которые содержат информацию о фактических продажах товаров, включая артикул, дату продажи, "
            )
            f.write(
                "количество, сумму продажи и другие важные атрибуты транзакции.\n\n"
            )

            # Структура таблицы
            f.write("## Структура таблицы\n\n")
            if columns_info:
                f.write("| Имя столбца | Тип данных | Длина | Nullable |\n")
                f.write("|-------------|------------|-------|----------|\n")
                for col in columns_info:
                    f.write(
                        f"| {col['column_name']} | {col['data_type']} | {col['data_length']} | {col['nullable']} |\n"
                    )
            else:
                f.write("Не удалось получить информацию о структуре таблицы.\n")

            # Ограничения таблицы
            f.write("\n## Ограничения таблицы\n\n")
            if constraints_info:
                f.write("| Имя ограничения | Тип ограничения | Столбец |\n")
                f.write("|-----------------|-----------------|--------|\n")
                for con in constraints_info:
                    f.write(
                        f"| {con['constraint_name']} | {con['constraint_type']} | {con['column_name']} |\n"
                    )
            else:
                f.write("Не удалось получить информацию об ограничениях таблицы.\n")

            # Связи таблицы
            f.write("\n## Связи с другими таблицами\n\n")
            if relationships_info:
                f.write(
                    "| Таблица | FK | FK столбец | Связанная таблица | Связанный столбец | Тип связи |\n"
                )
                f.write(
                    "|---------|----|-----------|--------------------|------------------|----------|\n"
                )
                for rel in relationships_info:
                    f.write(
                        f"| {rel['referenced_table']} | {rel['fk_constraint']} | {rel['fk_column']} | "
                    )
                    f.write(
                        f"{rel['referenced_by_table']} | {rel['referenced_column']} | {rel['dependency_type']} |\n"
                    )
            else:
                f.write(
                    "Не удалось получить информацию о связях таблицы или таблица не имеет внешних ключей.\n"
                )

            # Статистика по данным
            f.write("\n## Статистика по данным с RECTYPE = 1\n\n")
            if stats:
                f.write("| Метрика | Значение |\n")
                f.write("|---------|----------|\n")
                f.write(f"| Всего записей | {stats['total_records']} |\n")
                f.write(f"| Минимальная дата | {stats['min_date']} |\n")
                f.write(f"| Максимальная дата | {stats['max_date']} |\n")
                f.write(f"| Общее количество | {stats['total_quantity']} |\n")
                f.write(f"| Общая сумма | {stats['total_sum']} |\n")
                f.write(f"| Средняя сумма | {stats['avg_sum']} |\n")
                f.write(f"| Уникальных артикулов | {stats['unique_articles']} |\n")
                f.write(f"| Уникальных продаж | {stats['unique_sales']} |\n")
            else:
                f.write("Не удалось получить статистику по данным таблицы.\n")

            # Возможные аналитические отчеты
            f.write("\n## Возможные аналитические отчеты\n\n")
            f.write(
                "На основе данных таблицы FFMAPREP с RECTYPE = 1 можно создать следующие аналитические отчеты:\n\n"
            )
            f.write(
                "1. **Анализ продаж по периодам** - динамика продаж по дням, неделям, месяцам и годам.\n"
            )
            f.write(
                "2. **Анализ продаж по товарам** - рейтинг самых продаваемых товаров по количеству и сумме.\n"
            )
            f.write(
                "3. **Анализ продаж по клиентам** - рейтинг клиентов по объему закупок.\n"
            )
            f.write(
                "4. **Анализ продаж по операторам** - эффективность работы операторов/продавцов.\n"
            )
            f.write(
                "5. **Анализ продаж по типам оплаты** - соотношение наличных и безналичных платежей.\n"
            )
            f.write(
                "6. **Анализ прибыльности продаж** - расчет маржинальности по товарам и категориям.\n"
            )
            f.write("7. **Анализ возвратов** - статистика по возвратам товаров.\n")
            f.write(
                "8. **Анализ продаж по локациям** - распределение продаж по торговым точкам.\n"
            )
            f.write(
                "9. **Прогнозирование продаж** - построение прогнозных моделей на основе исторических данных.\n"
            )
            f.write(
                "10. **ABC-анализ товаров** - классификация товаров по их вкладу в общий объем продаж.\n"
            )

            logger.info(
                "Markdown-файл с результатами анализа успешно создан: ffmaprep_analysis.md"
            )
    except Exception as e:
        logger.error(f"Ошибка при создании markdown-файла: {str(e)}")


def main():
    """Основная функция для анализа таблицы FFMAPREP."""
    logger.info("Начало анализа таблицы FFMAPREP")

    # Получение структуры таблицы
    columns_info = get_table_structure()
    print("\n=== Структура таблицы FFMAPREP ===")
    if columns_info:
        headers = ["Имя столбца", "Тип данных", "Длина", "Nullable"]
        table_data = [
            (col["column_name"], col["data_type"], col["data_length"], col["nullable"])
            for col in columns_info
        ]
        print(tabulate(table_data, headers=headers, tablefmt="grid"))
    else:
        print("Не удалось получить информацию о структуре таблицы")

    # Получение ограничений таблицы
    constraints_info = get_table_constraints()
    print("\n=== Ограничения таблицы FFMAPREP ===")
    if constraints_info:
        headers = ["Имя ограничения", "Тип ограничения", "Столбец"]
        table_data = [
            (con["constraint_name"], con["constraint_type"], con["column_name"])
            for con in constraints_info
        ]
        print(tabulate(table_data, headers=headers, tablefmt="grid"))
    else:
        print("Не удалось получить информацию об ограничениях таблицы")

    # Получение связей таблицы
    relationships_info = get_table_relationships()
    print("\n=== Связи таблицы FFMAPREP с другими таблицами ===")
    if relationships_info:
        headers = [
            "Таблица",
            "FK",
            "FK столбец",
            "Связанная таблица",
            "Связанный столбец",
            "Тип связи",
        ]
        table_data = [
            (
                rel["referenced_table"],
                rel["fk_constraint"],
                rel["fk_column"],
                rel["referenced_by_table"],
                rel["referenced_column"],
                rel["dependency_type"],
            )
            for rel in relationships_info
        ]
        print(tabulate(table_data, headers=headers, tablefmt="grid"))
    else:
        print("Не удалось получить информацию о связях таблицы")

    # Получение статистики по данным с RECTYPE = 1
    stats = get_table_statistics(rectype=1)
    print("\n=== Статистика по данным таблицы FFMAPREP с RECTYPE = 1 ===")
    if stats:
        # Преобразуем словарь в таблицу для вывода
        headers = ["Метрика", "Значение"]
        table_data = [
            ("Всего записей", stats["total_records"]),
            ("Минимальная дата", stats["min_date"]),
            ("Максимальная дата", stats["max_date"]),
            ("Общее количество", stats["total_quantity"]),
            ("Общая сумма", stats["total_sum"]),
            ("Средняя сумма", stats["avg_sum"]),
            ("Уникальных артикулов", stats["unique_articles"]),
            ("Уникальных продаж", stats["unique_sales"]),
        ]
        print(tabulate(table_data, headers=headers, tablefmt="grid"))
    else:
        print("Не удалось получить статистику по данным таблицы")

    # Получение образца данных
    data_sample = get_table_data_sample(rectype=1)
    print("\n=== Образец данных из таблицы FFMAPREP с RECTYPE = 1 ===")
    if data_sample:
        # Получаем имена столбцов из первого результата
        if hasattr(data_sample[0], "_fields"):
            headers = data_sample[0]._fields
            table_data = [row for row in data_sample]
        else:
            # Если результат в виде кортежей, используем имена столбцов из columns_info
            headers = [col["column_name"] for col in columns_info]
            table_data = [row for row in data_sample]

        print(tabulate(table_data, headers=headers, tablefmt="grid"))
    else:
        print("Не удалось получить образец данных из таблицы")

    # Создание markdown-файла с результатами анализа
    create_markdown_report(
        columns_info, constraints_info, relationships_info, stats, data_sample
    )

    logger.info("Анализ таблицы FFMAPREP завершен")


if __name__ == "__main__":
    main()
