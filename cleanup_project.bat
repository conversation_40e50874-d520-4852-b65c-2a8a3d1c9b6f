@echo off
setlocal enabledelayedexpansion

:: =============================================================================
:: Database Explorer MCP - Project Cleanup Script
:: =============================================================================
:: This script safely moves unnecessary files to to_delete folder
:: Preserves all important MCP server components and documentation
:: =============================================================================

echo.
echo ================================================================================
echo                    Database Explorer MCP - Project Cleanup
echo ================================================================================
echo.

:: Check that script is run from project root
if not exist "mcp_server.py" (
    echo [ERROR] Script must be run from Database Explorer MCP project root
    echo         Make sure mcp_server.py file is in current directory
    echo.
    pause
    exit /b 1
)

echo [OK] Database Explorer MCP project found
echo.

:: Create to_delete folder
set "DELETE_DIR=to_delete"
if not exist "%DELETE_DIR%" (
    mkdir "%DELETE_DIR%"
    echo [CREATED] Folder: %DELETE_DIR%
) else (
    echo [EXISTS] Folder %DELETE_DIR% already exists
)
echo.

:: Counter for moved files
set /a MOVED_COUNT=0

echo [INFO] Starting project cleanup...
echo.

:: =============================================================================
:: 1. Перемещение файлов анализа таблиц (*.md кроме документации)
:: =============================================================================
echo 📋 Перемещение файлов анализа таблиц...

for %%f in (ffmaprep_analysis.md supermag_*.md *_analysis.md) do (
    if exist "%%f" (
        move "%%f" "%DELETE_DIR%\" >nul 2>&1
        if !errorlevel! equ 0 (
            echo    ✓ Перемещен: %%f
            set /a MOVED_COUNT+=1
        )
    )
)

:: =============================================================================
:: 2. Перемещение старых конфигураций и промптов
:: =============================================================================
echo.
echo 📄 Перемещение старых конфигураций и промптов...

for %%f in (old_*.md *_prompt.md oracle_connection_prompt.md) do (
    if exist "%%f" (
        move "%%f" "%DELETE_DIR%\" >nul 2>&1
        if !errorlevel! equ 0 (
            echo    ✓ Перемещен: %%f
            set /a MOVED_COUNT+=1
        )
    )
)

:: =============================================================================
:: 3. Перемещение тестовых файлов (кроме test_mcp_server.py)
:: =============================================================================
echo.
echo 🧪 Перемещение тестовых файлов...

for %%f in (my_test.py test_connection_pool.py test_drcp_connection.py test_oracle_connection.py query_*.py) do (
    if exist "%%f" (
        move "%%f" "%DELETE_DIR%\" >nul 2>&1
        if !errorlevel! equ 0 (
            echo    ✓ Перемещен: %%f
            set /a MOVED_COUNT+=1
        )
    )
)

:: =============================================================================
:: 4. Перемещение кэша Python
:: =============================================================================
echo.
echo 🗂️  Перемещение кэша Python...

if exist "__pycache__" (
    move "__pycache__" "%DELETE_DIR%\" >nul 2>&1
    if !errorlevel! equ 0 (
        echo    ✓ Перемещена папка: __pycache__
        set /a MOVED_COUNT+=1
    )
)

:: Поиск и перемещение __pycache__ в подпапках
for /d /r . %%d in (__pycache__) do (
    if exist "%%d" (
        set "rel_path=%%d"
        set "rel_path=!rel_path:%CD%\=!"
        if not "!rel_path!"=="!rel_path:to_delete=!" (
            rem Пропускаем папки внутри to_delete
        ) else (
            move "%%d" "%DELETE_DIR%\" >nul 2>&1
            if !errorlevel! equ 0 (
                echo    ✓ Перемещена папка: !rel_path!
                set /a MOVED_COUNT+=1
            )
        )
    )
)

:: Перемещение .pyc и .pyo файлов
for /r . %%f in (*.pyc *.pyo) do (
    set "file_path=%%f"
    set "rel_path=!file_path:%CD%\=!"
    if not "!rel_path!"=="!rel_path:to_delete=!" (
        rem Пропускаем файлы внутри to_delete
    ) else (
        move "%%f" "%DELETE_DIR%\" >nul 2>&1
        if !errorlevel! equ 0 (
            echo    ✓ Перемещен: !rel_path!
            set /a MOVED_COUNT+=1
        )
    )
)

:: =============================================================================
:: 5. Перемещение логов
:: =============================================================================
echo.
echo 📊 Перемещение логов...

if exist "logs" (
    move "logs" "%DELETE_DIR%\" >nul 2>&1
    if !errorlevel! equ 0 (
        echo    ✓ Перемещена папка: logs
        set /a MOVED_COUNT+=1
    )
)

for %%f in (*.log) do (
    if exist "%%f" (
        move "%%f" "%DELETE_DIR%\" >nul 2>&1
        if !errorlevel! equ 0 (
            echo    ✓ Перемещен: %%f
            set /a MOVED_COUNT+=1
        )
    )
)

:: =============================================================================
:: 6. Перемещение виртуального окружения
:: =============================================================================
echo.
echo 🐍 Перемещение виртуального окружения...

if exist "venv" (
    echo    ⏳ Перемещение папки venv (может занять время)...
    move "venv" "%DELETE_DIR%\" >nul 2>&1
    if !errorlevel! equ 0 (
        echo    ✓ Перемещена папка: venv
        set /a MOVED_COUNT+=1
    ) else (
        echo    ⚠️  Не удалось переместить venv (возможно, используется)
    )
)

:: =============================================================================
:: 7. Перемещение дополнительных ненужных файлов
:: =============================================================================
echo.
echo 🗃️  Перемещение дополнительных файлов...

for %%f in (_requirements.txt pdf_extractor.py) do (
    if exist "%%f" (
        move "%%f" "%DELETE_DIR%\" >nul 2>&1
        if !errorlevel! equ 0 (
            echo    ✓ Перемещен: %%f
            set /a MOVED_COUNT+=1
        )
    )
)

:: =============================================================================
:: Вывод результатов
:: =============================================================================
echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                              РЕЗУЛЬТАТЫ ОЧИСТКИ                             ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.

echo 📊 Статистика:
echo    • Перемещено файлов/папок: !MOVED_COUNT!
echo    • Папка назначения: %DELETE_DIR%
echo.

echo ✅ СОХРАНЕНЫ важные файлы проекта:
echo.
echo 📁 Основные модули:
if exist "mcp_server.py" echo    ✓ mcp_server.py
if exist "db_explorer.py" echo    ✓ db_explorer.py
if exist "install_mcp.py" echo    ✓ install_mcp.py
if exist "test_mcp_server.py" echo    ✓ test_mcp_server.py
echo.

echo 📁 Директории с кодом:
if exist "mcp_tools" echo    ✓ mcp_tools/
if exist "connectors" echo    ✓ connectors/
if exist "exporters" echo    ✓ exporters/
if exist "models" echo    ✓ models/
if exist "integrations" echo    ✓ integrations/
echo.

echo 📁 Конфигурация:
if exist ".env.example" echo    ✓ .env.example
if exist "requirements.txt" echo    ✓ requirements.txt
if exist "requirements_mcp.txt" echo    ✓ requirements_mcp.txt
if exist "config_db.py" echo    ✓ config_db.py
echo.

echo 📁 Документация:
if exist "README.md" echo    ✓ README.md
if exist "README_MCP.md" echo    ✓ README_MCP.md
if exist "QUICKSTART.md" echo    ✓ QUICKSTART.md
if exist "integrations\INTEGRATION_GUIDE.md" echo    ✓ integrations\INTEGRATION_GUIDE.md
if exist "integrations\USAGE_EXAMPLES.md" echo    ✓ integrations\USAGE_EXAMPLES.md
echo.

if !MOVED_COUNT! gtr 0 (
    echo 🎉 Очистка проекта завершена успешно!
    echo.
    echo 💡 Что дальше:
    echo    1. Проверьте содержимое папки '%DELETE_DIR%'
    echo    2. Если все в порядке, можете удалить папку '%DELETE_DIR%'
    echo    3. Или оставьте как резервную копию
    echo.
    echo 🚀 Проект готов к использованию:
    echo    • python test_mcp_server.py  - тестирование MCP сервера
    echo    • python install_mcp.py     - установка в Claude Desktop
    echo    • python mcp_server.py      - запуск MCP сервера
) else (
    echo ℹ️  Файлы для перемещения не найдены - проект уже очищен!
)

echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                                 ЗАВЕРШЕНО                                   ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.

pause
