# DB Metadata Explorer MCP

Инструмент для исследования структуры реляционных баз данных и экспорта метаданных в Markdown.

## Описание

DB Metadata Explorer MCP - это инструмент, который позволяет:

1. Исследовать структуру реляционных баз данных (таблицы, столбцы, ограничения, индексы)
2. Извлекать метаданные из различных СУБД (Oracle, MySQL, PostgreSQL, ClickHouse)
3. Сохранять полученную информацию в структурированном виде в Markdown-файлы
4. Анализировать зависимости между таблицами
5. Получать полную информацию о схеме базы данных

## Особенности

- Модульная архитектура с поддержкой различных СУБД через абстрактные интерфейсы
- Параллельная обработка для улучшения производительности
- Экспорт метаданных в удобный для чтения формат Markdown
- Детальный анализ структуры таблиц, включая ограничения и индексы
- Анализ зависимостей между таблицами (внешние ключи)

## Требования

- Python 3.8+
- Необходимые Python-библиотеки (см. requirements.txt)
- Доступ к СУБД (Oracle, MySQL, PostgreSQL, ClickHouse)

## Установка

1. Клонировать репозиторий:
```bash
git clone https://github.com/yourusername/db-metadata-explorer-mcp.git
cd db-metadata-explorer-mcp
```

2. Создать виртуальное окружение и активировать его:
```bash
python -m venv venv
source venv/bin/activate  # для Linux/Mac
venv\Scripts\activate     # для Windows
```

3. Установить зависимости:
```bash
pip install -r requirements.txt
```

4. Настроить подключение к базе данных в файле `.env`:
```
# Для Oracle
ORACLE_HOST=hostname
ORACLE_PORT=1521
ORACLE_USER=username
ORACLE_PASS=password
ORACLE_BASE=service_name

# Для MySQL (будет добавлено в будущих версиях)
# MYSQL_HOST=hostname
# MYSQL_PORT=3306
# MYSQL_USER=username
# MYSQL_PASS=password
# MYSQL_DATABASE=database_name

# Для PostgreSQL (будет добавлено в будущих версиях)
# POSTGRES_HOST=hostname
# POSTGRES_PORT=5432
# POSTGRES_USER=username
# POSTGRES_PASS=password
# POSTGRES_DATABASE=database_name

# Для ClickHouse (будет добавлено в будущих версиях)
# CLICKHOUSE_HOST=hostname
# CLICKHOUSE_PORT=9000
# CLICKHOUSE_USER=username
# CLICKHOUSE_PASS=password
# CLICKHOUSE_DATABASE=database_name
```

## Использование

### Исследование структуры базы данных

```bash
python db_explorer.py --db-type oracle --schema SCHEMA_NAME
```

### Исследование конкретной таблицы

```bash
python db_explorer.py --db-type oracle --table TABLE_NAME --schema SCHEMA_NAME
```

### Вывод списка доступных схем

```bash
python db_explorer.py --db-type oracle --list-schemas
```

### Вывод списка таблиц в схеме

```bash
python db_explorer.py --db-type oracle --list-tables --schema SCHEMA_NAME
```

### Экспорт метаданных в указанную директорию

```bash
python db_explorer.py --db-type oracle --schema SCHEMA_NAME --output-dir ./metadata
```

### Параллельная обработка

```bash
python db_explorer.py --db-type oracle --schema SCHEMA_NAME --workers 8
```

## Структура проекта

- `db_explorer.py` - основной модуль для исследования структуры базы данных
- `config_db.py` - конфигурация подключения к базе данных Oracle
- `connectors/` - модули для подключения к различным СУБД
  - `base.py` - абстрактный базовый класс для коннекторов
  - `oracle.py` - коннектор для Oracle
  - `factory.py` - фабрика для создания коннекторов
- `exporters/` - модули для экспорта метаданных
  - `markdown.py` - экспортер в формат Markdown
- `models/` - модели данных
  - `metadata.py` - модели для представления метаданных
- `requirements.txt` - список зависимостей

## Примеры

### Пример исследования схемы Oracle

```bash
python db_explorer.py --db-type oracle --schema SUPERMAG --workers 8 --output-dir ./metadata
```

### Пример исследования конкретной таблицы

```bash
python db_explorer.py --db-type oracle --table FFMAPREP --schema SUPERMAG
```

### Пример вывода списка таблиц

```bash
python db_explorer.py --db-type oracle --list-tables --schema SUPERMAG
```

## Формат выходных данных

Для каждой таблицы создается отдельный Markdown-файл со следующей структурой:

1. **Основная информация** - общие сведения о таблице (количество строк, дата последнего анализа и т.д.)
2. **Столбцы** - список столбцов с типами данных и другими атрибутами
3. **Ограничения** - первичные ключи, внешние ключи, уникальные ограничения и проверки
4. **Индексы** - список индексов с указанием типа и столбцов
5. **Зависимости** - связи с другими таблицами (внешние ключи)

Дополнительно создается общий файл с информацией о схеме, содержащий список всех таблиц и их основные характеристики.

## Расширение функциональности

Для добавления поддержки новой СУБД необходимо:

1. Создать новый класс-коннектор в директории `connectors/`, наследующийся от `DatabaseConnector`
2. Реализовать все абстрактные методы базового класса
3. Добавить новый тип базы данных в фабрику `DatabaseConnectorFactory`

## Лицензия

MIT

## Авторы

- Ваше имя - [<EMAIL>](mailto:<EMAIL>)
