# Database Explorer MCP Server

MCP (Model Context Protocol) сервер для исследования структуры реляционных баз данных, предназначенный для интеграции с ИИ агентами.

## Описание

Database Explorer MCP Server предоставляет стандартизированный интерфейс для ИИ агентов для исследования структуры баз данных. Сервер поддерживает различные СУБД и обеспечивает безопасный доступ к метаданным базы данных.

## Возможности

### Инструменты подключения
- `connect_database` - Подключение к базе данных
- `disconnect_database` - Отключение от базы данных
- `get_connection_status` - Проверка статуса подключения

### Инструменты для работы со схемами
- `list_schemas` - Получение списка схем
- `get_schema_info` - Детальная информация о схеме
- `list_tables_in_schema` - Список таблиц в схеме
- `compare_schemas` - Сравнение двух схем

### Инструменты для работы с таблицами
- `get_table_structure` - Полная структура таблицы
- `get_table_columns` - Информация о столбцах
- `get_table_constraints` - Ограничения таблицы
- `get_table_indexes` - Индексы таблицы
- `get_table_dependencies` - Зависимости таблицы
- `get_table_sample_data` - Образец данных
- `analyze_table_relationships` - Анализ связей таблицы

### Инструменты для выполнения запросов
- `execute_safe_query` - Безопасное выполнение SELECT запросов

## Поддерживаемые СУБД

- ✅ Oracle Database
- 🚧 MySQL (планируется)
- 🚧 PostgreSQL (планируется)
- 🚧 ClickHouse (планируется)

## Установка и настройка

### 1. Установка зависимостей

```bash
pip install -r requirements_mcp.txt
```

### 2. Настройка переменных окружения

Скопируйте файл `.env.example` в `.env` и заполните своими значениями:

```bash
cp .env.example .env
```

Отредактируйте `.env` файл:

```env
DB_TYPE=oracle
DB_HOST=your_host
DB_PORT=1521
DB_USER=your_username
DB_PASSWORD=your_password
DB_SERVICE=your_service_name
```

### 3. Запуск сервера

#### Stdio транспорт (по умолчанию)
```bash
python mcp_server.py
```

#### SSE транспорт
```bash
python mcp_server.py --transport sse --port 8000
```

#### Использование uv
```bash
uv run mcp_server.py
```

## Конфигурация клиента

### Claude Desktop (Stdio)

Добавьте в конфигурацию Claude Desktop:

```json
{
  "mcpServers": {
    "database-explorer": {
      "command": "python",
      "args": ["path/to/mcp_server.py"],
      "env": {
        "DB_TYPE": "oracle",
        "DB_HOST": "your_host",
        "DB_PORT": "1521",
        "DB_USER": "your_username",
        "DB_PASSWORD": "your_password",
        "DB_SERVICE": "your_service_name"
      }
    }
  }
}
```

### Claude Desktop (SSE)

```json
{
  "mcpServers": {
    "database-explorer": {
      "transport": "sse",
      "url": "http://localhost:8000/sse"
    }
  }
}
```

## Безопасность

### Ограничения запросов
- Разрешены только SELECT запросы
- Максимальное количество возвращаемых строк: 1000
- Запрещены DDL и DML операции
- Валидация SQL запросов

### Логирование
- Все операции логируются
- Ротация логов по размеру
- Настраиваемый уровень логирования

## Примеры использования

### Подключение к базе данных

```python
# Подключение с параметрами
result = connect_database(
    db_type="oracle",
    host="localhost",
    port=1521,
    user="username",
    password="password",
    service="service_name"
)

# Подключение с переменными окружения
result = connect_database(db_type="oracle")
```

### Исследование схемы

```python
# Список всех схем
schemas = list_schemas()

# Информация о конкретной схеме
schema_info = get_schema_info("SCHEMA_NAME")

# Таблицы в схеме
tables = list_tables_in_schema("SCHEMA_NAME")
```

### Анализ таблицы

```python
# Полная структура таблицы
structure = get_table_structure("TABLE_NAME", "SCHEMA_NAME")

# Столбцы таблицы
columns = get_table_columns("TABLE_NAME", "SCHEMA_NAME")

# Образец данных
sample = get_table_sample_data("TABLE_NAME", "SCHEMA_NAME", limit=20)
```

### Выполнение запросов

```python
# Безопасный SELECT запрос
result = execute_safe_query(
    "SELECT * FROM SCHEMA_NAME.TABLE_NAME WHERE ROWNUM <= 10"
)
```

## Разработка

### Структура проекта

```
mcp_server.py           # Основной MCP сервер
mcp_tools/              # Модули инструментов
├── __init__.py
├── database_tools.py   # Инструменты подключения
├── schema_tools.py     # Инструменты схем
└── table_tools.py      # Инструменты таблиц
requirements_mcp.txt    # Зависимости MCP
.env.example           # Пример конфигурации
README_MCP.md          # Документация
```

### Добавление новых СУБД

1. Создайте коннектор в `connectors/`
2. Добавьте поддержку в `DatabaseConnectorFactory`
3. Обновите документацию

### Тестирование

```bash
# Тестирование с Inspector
mcp dev mcp_server.py

# Установка в Claude Desktop
mcp install mcp_server.py --name "Database Explorer"
```

## Логирование

Логи сохраняются в директории `logs/`:
- `mcp_server.log` - основные логи сервера
- Ротация по размеру (100 MB)
- Хранение 30 дней

## Поддержка

При возникновении проблем:
1. Проверьте логи в директории `logs/`
2. Убедитесь в правильности конфигурации
3. Проверьте доступность базы данных
4. Создайте issue в репозитории проекта
