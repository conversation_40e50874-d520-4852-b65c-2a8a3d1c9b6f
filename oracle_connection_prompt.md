# Инструкция по использованию соединений с базами данных

## Общие сведения
- Для работы с базами данных использовать QDRANT вместо Oracle.
- В проекте настроен и используется пул соединений в SQLAlchemy для работы с QDRANT.
- На сервере Oracle пул DRCP (SYS_DEFAULT_CONNECTION_POOL) находится в неактивном состоянии.
- Для работы с векторной базой данных Qdrant настроено подключение, которое успешно работает.

## Импорт и использование соединения с Oracle

Для использования настроенного соединения с базой данных Oracle в вашем коде:

```python
# Импорт настроенной сессии и движка
from config_db import session, engine
from sqlalchemy import text

# Пример выполнения простого запроса
result = session.execute(text("SELECT * FROM your_table"))
rows = result.fetchall()

# Пример выполнения запроса с параметрами
params = {"param_name": "param_value"}
result = session.execute(
    text("SELECT * FROM your_table WHERE column = :param_name"),
    params
)

# Пример выполнения DML-запросов (INSERT, UPDATE, DELETE)
# с автоматическим управлением транзакциями
try:
    session.execute(
        text("INSERT INTO your_table (column1, column2) VALUES (:val1, :val2)"),
        {"val1": "value1", "val2": "value2"}
    )
    session.commit()  # Фиксация изменений
except Exception as e:
    session.rollback()  # Откат при ошибке
    raise

# Пример использования контекстного менеджера для автоматического закрытия соединения
with engine.connect() as connection:
    result = connection.execute(text("SELECT * FROM your_table"))
    rows = result.fetchall()
```

## Особенности и рекомендации

1. **Управление транзакциями**:
   - Всегда используйте `session.commit()` после DML-операций
   - Используйте `session.rollback()` в блоке `except` для отката изменений при ошибках
   - Для длительных операций рекомендуется периодически выполнять `session.commit()`

2. **Эффективное использование пула соединений**:
   - Не держите соединения открытыми дольше, чем необходимо
   - Используйте контекстные менеджеры (`with engine.connect() as connection:`)
   - Закрывайте сессии после использования (`session.close()`)

3. **Работа с большими объемами данных**:
   - Используйте пакетную обработку для больших наборов данных
   - Для выборки больших результатов используйте `yield_per()` или итерацию по курсору
   - Пример: `for row in session.execute(text("SELECT * FROM large_table")).yield_per(1000):`

4. **Обработка ошибок**:
   - Всегда оборачивайте операции с базой данных в блоки try-except
   - Логируйте ошибки с достаточной информацией для отладки
   - Проверяйте соединение перед использованием (это уже настроено через `pool_pre_ping=True`)

5. **Оптимизация запросов**:
   - Используйте подготовленные запросы с параметрами
   - Избегайте конструкций `SELECT *`, указывайте только необходимые столбцы
   - Используйте индексы и оптимизируйте условия WHERE

## Пример полного рабочего кода

```python
from config_db import session, engine
from sqlalchemy import text
from loguru import logger
import os

# Настройка логирования
os.makedirs("logs", exist_ok=True)
logger.add(
    "logs/database_operations.log",
    rotation="100 MB",
    retention="30 days",
    format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {function}:{line} | {message}",
    level="INFO"
)

def execute_select_query(query, params=None):
    """Выполнение SELECT-запроса с обработкой ошибок."""
    try:
        result = session.execute(text(query), params or {})
        return result.fetchall()
    except Exception as e:
        logger.error(f"Ошибка при выполнении SELECT-запроса: {str(e)}")
        logger.error(f"Запрос: {query}")
        logger.error(f"Параметры: {params}")
        raise

def execute_dml_query(query, params=None):
    """Выполнение DML-запроса (INSERT, UPDATE, DELETE) с управлением транзакциями."""
    try:
        result = session.execute(text(query), params or {})
        affected_rows = result.rowcount
        session.commit()
        logger.info(f"DML-запрос выполнен успешно. Затронуто строк: {affected_rows}")
        return affected_rows
    except Exception as e:
        session.rollback()
        logger.error(f"Ошибка при выполнении DML-запроса: {str(e)}")
        logger.error(f"Запрос: {query}")
        logger.error(f"Параметры: {params}")
        raise

def batch_insert(table_name, columns, values_list):
    """Пакетная вставка данных в таблицу."""
    try:
        # Формирование запроса для пакетной вставки
        placeholders = ", ".join([f":{col}" for col in columns])
        columns_str = ", ".join(columns)
        query = f"INSERT INTO {table_name} ({columns_str}) VALUES ({placeholders})"

        # Выполнение пакетной вставки
        for i, values in enumerate(values_list):
            params = dict(zip(columns, values))
            session.execute(text(query), params)

            # Коммит каждые 1000 записей или в конце списка
            if (i + 1) % 1000 == 0 or i == len(values_list) - 1:
                session.commit()
                logger.info(f"Вставлено {i + 1} записей в таблицу {table_name}")

        return len(values_list)
    except Exception as e:
        session.rollback()
        logger.error(f"Ошибка при пакетной вставке в таблицу {table_name}: {str(e)}")
        raise

# Пример использования функций
if __name__ == "__main__":
    try:
        # Пример SELECT-запроса
        users = execute_select_query(
            "SELECT user_id, username FROM users WHERE status = :status",
            {"status": "active"}
        )

        # Пример DML-запроса
        affected = execute_dml_query(
            "UPDATE users SET last_login = SYSDATE WHERE user_id = :user_id",
            {"user_id": 123}
        )

        # Пример пакетной вставки
        data = [
            (1, "User 1", "<EMAIL>"),
            (2, "User 2", "<EMAIL>"),
            (3, "User 3", "<EMAIL>")
        ]
        batch_insert("users", ["user_id", "username", "email"], data)

    except Exception as e:
        logger.error(f"Произошла ошибка: {str(e)}")
    finally:
        # Закрытие сессии в конце работы
        session.close()
```

## Важные замечания

1. Соединение с базой данных уже настроено в файле `config_db.py` с оптимальными параметрами для пула соединений и DRCP.
2. Не создавайте новые соединения с базой данных, используйте существующие объекты `session` и `engine`.
3. Для долгоживущих приложений периодически проверяйте состояние соединений.
4. При возникновении проблем с соединением проверьте логи в директории `logs/`.
5. Пул соединений настроен на автоматическую проверку соединений перед использованием (`pool_pre_ping=True`).

## Диагностика проблем с соединением

Если возникают проблемы с соединением, выполните следующие шаги:

1. Проверьте логи в файле `logs/database_config.log`
2. Убедитесь, что переменные окружения для подключения к базе данных корректно настроены
3. Проверьте статус DRCP на сервере Oracle:
   ```sql
   SELECT * FROM DBA_CPOOL_INFO;
   ```
4. Проверьте доступность сервера Oracle:
   ```bash
   ping <ORACLE_HOST>
   ```
5. Проверьте количество активных соединений:
   ```sql
   SELECT COUNT(*) FROM V$SESSION WHERE USERNAME = '<ORACLE_USER>';
   ```

## Исследование базы данных

### Анализ структуры базы данных

```python
def get_table_info(schema_name=None):
    """Получение информации о таблицах в схеме."""
    query = """
    SELECT table_name, num_rows, last_analyzed
    FROM all_tables
    WHERE owner = :schema
    ORDER BY table_name
    """
    return execute_select_query(query, {"schema": schema_name or session.get_bind().url.username.upper()})

def get_column_info(table_name, schema_name=None):
    """Получение информации о столбцах таблицы."""
    query = """
    SELECT column_name, data_type, data_length, nullable, data_default
    FROM all_tab_columns
    WHERE table_name = :table
    AND owner = :schema
    ORDER BY column_id
    """
    return execute_select_query(
        query,
        {
            "table": table_name.upper(),
            "schema": schema_name or session.get_bind().url.username.upper()
        }
    )

def get_constraints_info(table_name, schema_name=None):
    """Получение информации об ограничениях таблицы."""
    query = """
    SELECT c.constraint_name, c.constraint_type, c.search_condition,
           c.r_owner, c.r_constraint_name,
           cc.column_name
    FROM all_constraints c
    JOIN all_cons_columns cc ON c.constraint_name = cc.constraint_name
    WHERE c.table_name = :table
    AND c.owner = :schema
    ORDER BY c.constraint_type, cc.position
    """
    return execute_select_query(
        query,
        {
            "table": table_name.upper(),
            "schema": schema_name or session.get_bind().url.username.upper()
        }
    )

def get_indexes_info(table_name, schema_name=None):
    """Получение информации об индексах таблицы."""
    query = """
    SELECT i.index_name, i.index_type, i.uniqueness,
           ic.column_name, ic.descend
    FROM all_indexes i
    JOIN all_ind_columns ic ON i.index_name = ic.index_name
    WHERE i.table_name = :table
    AND i.owner = :schema
    ORDER BY i.index_name, ic.column_position
    """
    return execute_select_query(
        query,
        {
            "table": table_name.upper(),
            "schema": schema_name or session.get_bind().url.username.upper()
        }
    )

def analyze_table_dependencies(table_name, schema_name=None):
    """Анализ зависимостей таблицы (внешние ключи)."""
    query = """
    SELECT a.table_name as referenced_table,
           c.constraint_name as fk_constraint,
           acc.column_name as fk_column,
           r.table_name as referenced_by_table,
           rc.column_name as referenced_column
    FROM all_constraints a
    JOIN all_cons_columns acc ON a.constraint_name = acc.constraint_name
    JOIN all_constraints r ON a.r_constraint_name = r.constraint_name
    JOIN all_cons_columns rc ON r.constraint_name = rc.constraint_name
    WHERE a.constraint_type = 'R'
    AND (a.table_name = :table OR r.table_name = :table)
    AND a.owner = :schema
    ORDER BY a.table_name, acc.position
    """
    return execute_select_query(
        query,
        {
            "table": table_name.upper(),
            "schema": schema_name or session.get_bind().url.username.upper()
        }
    )
```

### Обновление векторной базы

```python
from datetime import datetime
from typing import List, Dict, Any

def generate_table_vector(table_info: Dict[str, Any], columns_info: List[Dict[str, Any]]):
    """Генерация векторного представления таблицы для поиска."""
    # Здесь должна быть реализация генерации векторов
    # с использованием выбранной библиотеки для работы с векторами
    pass

def update_vector_store(schema_name=None):
    """Обновление векторного хранилища на основе метаданных схемы."""
    try:
        # Получение информации о всех таблицах
        tables = get_table_info(schema_name)

        for table in tables:
            table_name = table['table_name']

            # Получение детальной информации о таблице
            columns = get_column_info(table_name, schema_name)
            constraints = get_constraints_info(table_name, schema_name)
            indexes = get_indexes_info(table_name, schema_name)
            dependencies = analyze_table_dependencies(table_name, schema_name)

            # Генерация векторного представления
            table_vector = generate_table_vector(
                table,
                {
                    'columns': columns,
                    'constraints': constraints,
                    'indexes': indexes,
                    'dependencies': dependencies
                }
            )

            # Сохранение векторного представления
            # Здесь должен быть код для сохранения векторов
            # в выбранное векторное хранилище

            logger.info(f"Обновлены векторы для таблицы {table_name}")

        logger.info("Векторное хранилище успешно обновлено")

    except Exception as e:
        logger.error(f"Ошибка при обновлении векторного хранилища: {str(e)}")
        raise
```

### Пример использования функций исследования

```python
# Получение информации о таблице
table_name = "EMPLOYEES"
schema_name = "HR"

# Базовая информация о таблице
table_info = get_table_info(schema_name)
print(f"Найдено таблиц: {len(table_info)}")

# Детальная информация о конкретной таблице
columns = get_column_info(table_name, schema_name)
print(f"Столбцы таблицы {table_name}:")
for col in columns:
    print(f"- {col['column_name']}: {col['data_type']}")

# Анализ зависимостей
deps = analyze_table_dependencies(table_name, schema_name)
print(f"\nЗависимости таблицы {table_name}:")
for dep in deps:
    print(f"- {dep['fk_constraint']}: {dep['fk_column']} -> {dep['referenced_table']}.{dep['referenced_column']}")

# Обновление векторного хранилища
update_vector_store(schema_name)
```

## Использование соединения с Qdrant

Для работы с векторной базой данных Qdrant используйте настроенный клиент:

```python
# Импорт настроенного клиента Qdrant
from config_db import qdrant_client
from qdrant_client.http.models import Distance, VectorParams

# Проверка, что клиент инициализирован
if qdrant_client is None:
    print("Клиент Qdrant не инициализирован")
else:
    # Получение списка коллекций
    collections_response = qdrant_client.get_collections()
    if hasattr(collections_response, 'collections'):
        collections = collections_response.collections
        print(f"Найдено {len(collections)} коллекций:")
        for collection in collections:
            print(f"- {collection.name}")

    # Работа с существующей коллекцией
    # Примечание: создание новых коллекций может вызвать ошибки из-за несовместимости версий
    collection_name = "documents"  # Используйте существующую коллекцию

    # Поиск векторов в существующей коллекции
    try:
        search_result = qdrant_client.search(
            collection_name=collection_name,
            query_vector=[0.1, 0.2, 0.3] * 128,  # Вектор размерности 384
            limit=5
        )
        print(f"Результаты поиска: {search_result}")
    except Exception as e:
        print(f"Ошибка при поиске: {str(e)}")

    # Добавление векторов в существующую коллекцию
    try:
        qdrant_client.upsert(
            collection_name=collection_name,
            points=[
                {
                    "id": "unique_id_123",
                    "vector": [0.1, 0.2, 0.3] * 128,  # Вектор размерности 384
                    "payload": {"text": "Пример текста", "metadata": {"key": "value"}}
                }
            ]
        )
        print("Вектор успешно добавлен")
    except Exception as e:
        print(f"Ошибка при добавлении вектора: {str(e)}")
```

### Рекомендации по работе с Qdrant

1. **Создание коллекций**:
   - Выбирайте подходящую размерность векторов в зависимости от используемой модели
   - Используйте подходящую метрику расстояния (COSINE, EUCLID, DOT)
   - Задавайте осмысленные имена коллекциям

2. **Работа с векторами**:
   - Добавляйте полезную информацию в payload для упрощения работы с результатами
   - Используйте batch-операции для массового добавления векторов
   - Для больших коллекций используйте индексы для ускорения поиска

3. **Поиск**:
   - Настраивайте параметр limit в зависимости от ваших потребностей
   - Используйте фильтры для уточнения результатов поиска
   - Для сложных запросов используйте рекомендованные параметры поиска

## Заключение

Используйте настроенные соединения с базами данных Oracle и Qdrant для всех операций в вашем проекте. Это обеспечит эффективное использование ресурсов, повысит производительность и надежность вашего приложения. Функции исследования базы данных помогут вам лучше понять структуру данных, а векторное хранилище Qdrant упростит поиск и анализ семантически похожей информации.
