#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Установщик Database Explorer MCP для Augment Code.
"""

import json
import os
import platform
from pathlib import Path


def get_augment_config_path():
    """Получает путь к конфигурации Augment Code."""
    system = platform.system()
    
    if system == "Windows":
        appdata = os.getenv("APPDATA")
        return Path(appdata) / "Augment" / "mcp_config.json"
    elif system == "Darwin":  # macOS
        home = Path.home()
        return home / "Library" / "Application Support" / "Augment" / "mcp_config.json"
    elif system == "Linux":
        home = Path.home()
        return home / ".config" / "Augment" / "mcp_config.json"
    else:
        raise Exception(f"Неподдерживаемая ОС: {system}")


def install_mcp_for_augment():
    """Устанавливает MCP сервер для Augment Code."""
    print("🚀 Установка Database Explorer MCP для Augment Code")
    print("=" * 60)
    
    try:
        # Получаем путь к конфигурации
        config_path = get_augment_config_path()
        
        print(f"📁 Путь к конфигурации: {config_path}")
        
        # Создаем директорию если не существует
        config_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Загружаем существующую конфигурацию
        config = {}
        if config_path.exists():
            try:
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                print("✅ Загружена существующая конфигурация")
            except json.JSONDecodeError:
                print("⚠️  Файл конфигурации поврежден, создается новый")
                config = {}
        else:
            print("📝 Создается новый файл конфигурации")
        
        # Получаем абсолютный путь к MCP серверу
        current_dir = Path(__file__).parent.parent.parent.absolute()
        mcp_server_path = current_dir / "mcp_server.py"
        
        if not mcp_server_path.exists():
            print(f"❌ MCP сервер не найден: {mcp_server_path}")
            return False
        
        # Инициализируем секцию mcpServers
        if "mcpServers" not in config:
            config["mcpServers"] = {}
        
        # Настройки для Database Explorer
        server_config = {
            "name": "Database Explorer",
            "description": "Исследование структуры реляционных баз данных",
            "command": "python",
            "args": [str(mcp_server_path)],
            "env": {
                "DB_TYPE": "oracle"
            },
            "capabilities": {
                "tools": True,
                "resources": False,
                "prompts": False,
                "sampling": False
            },
            "settings": {
                "autoStart": True,
                "logLevel": "info",
                "timeout": 30000,
                "retries": 3
            }
        }
        
        # Добавляем переменные окружения если они есть
        env_vars = ["DB_HOST", "DB_PORT", "DB_USER", "DB_PASSWORD", "DB_SERVICE", "DB_NAME"]
        for var in env_vars:
            if os.getenv(var):
                server_config["env"][var] = os.getenv(var)
        
        # Добавляем сервер в конфигурацию
        config["mcpServers"]["database-explorer"] = server_config
        
        # Добавляем глобальные настройки MCP
        if "mcpSettings" not in config:
            config["mcpSettings"] = {
                "globalTimeout": 60000,
                "maxConcurrentServers": 5,
                "enableLogging": True,
                "logDirectory": "./logs/mcp"
            }
        
        # Сохраняем конфигурацию
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        
        print("✅ MCP сервер успешно установлен для Augment Code!")
        print(f"📁 Конфигурация сохранена в: {config_path}")
        
        # Создаем файл с примерами использования
        examples_path = config_path.parent / "database_explorer_examples.md"
        with open(examples_path, 'w', encoding='utf-8') as f:
            f.write("""# Database Explorer MCP - Примеры использования в Augment Code

## Основные команды

### Подключение к базе данных
```
Подключись к базе данных Oracle с хостом localhost
```

### Исследование схем
```
Покажи все доступные схемы в базе данных
```

```
Получи детальную информацию о схеме SUPERMAG
```

### Работа с таблицами
```
Покажи все таблицы в схеме SUPERMAG
```

```
Получи полную структуру таблицы FFMAPREP в схеме SUPERMAG
```

```
Покажи столбцы таблицы USERS
```

### Анализ данных
```
Получи образец данных из таблицы PRODUCTS, лимит 20 строк
```

```
Проанализируй связи таблицы ORDERS с другими таблицами
```

### Выполнение запросов
```
Выполни безопасный запрос: SELECT COUNT(*) FROM DUAL
```

```
Выполни запрос для получения списка пользователей: SELECT username FROM all_users WHERE rownum <= 10
```

## Сложные сценарии

### Анализ схемы базы данных
```
Проанализируй схему INVENTORY:
1. Покажи все таблицы
2. Для каждой таблицы покажи количество столбцов
3. Найди таблицы с внешними ключами
4. Создай диаграмму связей
```

### Исследование производительности
```
Найди таблицы в схеме SALES с наибольшим количеством индексов
```

### Сравнение схем
```
Сравни схемы PROD и TEST, покажи различия в таблицах
```

## Доступные инструменты

1. **connect_database** - Подключение к БД
2. **list_schemas** - Список схем  
3. **get_schema_info** - Информация о схеме
4. **list_tables_in_schema** - Таблицы в схеме
5. **get_table_structure** - Структура таблицы
6. **get_table_columns** - Столбцы таблицы
7. **get_table_constraints** - Ограничения таблицы
8. **get_table_indexes** - Индексы таблицы
9. **get_table_dependencies** - Зависимости таблицы
10. **get_table_sample_data** - Образцы данных
11. **analyze_table_relationships** - Анализ связей
12. **compare_schemas** - Сравнение схем
13. **execute_safe_query** - Безопасные запросы
14. **get_connection_status** - Статус подключения
15. **disconnect_database** - Отключение от БД
""")
        
        print(f"📖 Примеры использования сохранены в: {examples_path}")
        print("\n🔄 Перезапустите Augment Code для применения изменений")
        
        return True
        
    except Exception as e:
        print(f"❌ Ошибка при установке: {e}")
        return False


if __name__ == "__main__":
    install_mcp_for_augment()
