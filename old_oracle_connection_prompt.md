# Инструкция по использованию соединения с базой данных Oracle

## Общие сведения
- Для работы с Oracle использовать уже настроенное подключение к базе данных.
- В проекте настроен и используется пул соединений в SQLAlchemy для работы с Oracle.
- На сервере Oracle пул DRCP (SYS_DEFAULT_CONNECTION_POOL) находится в активном состоянии.
- Соединение настроено с использованием класса соединений DRCP "ORACLE_MCP_APP".

## Импорт и использование соединения

Для использования настроенного соединения с базой данных Oracle в вашем коде:

```python
# Импорт настроенной сессии и движка
from config_db import session, engine
from sqlalchemy import text

# Пример выполнения простого запроса
result = session.execute(text("SELECT * FROM your_table"))
rows = result.fetchall()

# Пример выполнения запроса с параметрами
params = {"param_name": "param_value"}
result = session.execute(
    text("SELECT * FROM your_table WHERE column = :param_name"),
    params
)

# Пример выполнения DML-запросов (INSERT, UPDATE, DELETE)
# с автоматическим управлением транзакциями
try:
    session.execute(
        text("INSERT INTO your_table (column1, column2) VALUES (:val1, :val2)"),
        {"val1": "value1", "val2": "value2"}
    )
    session.commit()  # Фиксация изменений
except Exception as e:
    session.rollback()  # Откат при ошибке
    raise

# Пример использования контекстного менеджера для автоматического закрытия соединения
with engine.connect() as connection:
    result = connection.execute(text("SELECT * FROM your_table"))
    rows = result.fetchall()
```

## Особенности и рекомендации

1. **Управление транзакциями**:
   - Всегда используйте `session.commit()` после DML-операций
   - Используйте `session.rollback()` в блоке `except` для отката изменений при ошибках
   - Для длительных операций рекомендуется периодически выполнять `session.commit()`

2. **Эффективное использование пула соединений**:
   - Не держите соединения открытыми дольше, чем необходимо
   - Используйте контекстные менеджеры (`with engine.connect() as connection:`)
   - Закрывайте сессии после использования (`session.close()`)

3. **Работа с большими объемами данных**:
   - Используйте пакетную обработку для больших наборов данных
   - Для выборки больших результатов используйте `yield_per()` или итерацию по курсору
   - Пример: `for row in session.execute(text("SELECT * FROM large_table")).yield_per(1000):`

4. **Обработка ошибок**:
   - Всегда оборачивайте операции с базой данных в блоки try-except
   - Логируйте ошибки с достаточной информацией для отладки
   - Проверяйте соединение перед использованием (это уже настроено через `pool_pre_ping=True`)

5. **Оптимизация запросов**:
   - Используйте подготовленные запросы с параметрами
   - Избегайте конструкций `SELECT *`, указывайте только необходимые столбцы
   - Используйте индексы и оптимизируйте условия WHERE

## Пример полного рабочего кода

```python
from config_db import session, engine
from sqlalchemy import text
from loguru import logger
import os

# Настройка логирования
os.makedirs("logs", exist_ok=True)
logger.add(
    "logs/database_operations.log",
    rotation="100 MB",
    retention="30 days",
    format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {function}:{line} | {message}",
    level="INFO"
)

def execute_select_query(query, params=None):
    """Выполнение SELECT-запроса с обработкой ошибок."""
    try:
        result = session.execute(text(query), params or {})
        return result.fetchall()
    except Exception as e:
        logger.error(f"Ошибка при выполнении SELECT-запроса: {str(e)}")
        logger.error(f"Запрос: {query}")
        logger.error(f"Параметры: {params}")
        raise

def execute_dml_query(query, params=None):
    """Выполнение DML-запроса (INSERT, UPDATE, DELETE) с управлением транзакциями."""
    try:
        result = session.execute(text(query), params or {})
        affected_rows = result.rowcount
        session.commit()
        logger.info(f"DML-запрос выполнен успешно. Затронуто строк: {affected_rows}")
        return affected_rows
    except Exception as e:
        session.rollback()
        logger.error(f"Ошибка при выполнении DML-запроса: {str(e)}")
        logger.error(f"Запрос: {query}")
        logger.error(f"Параметры: {params}")
        raise

def batch_insert(table_name, columns, values_list):
    """Пакетная вставка данных в таблицу."""
    try:
        # Формирование запроса для пакетной вставки
        placeholders = ", ".join([f":{col}" for col in columns])
        columns_str = ", ".join(columns)
        query = f"INSERT INTO {table_name} ({columns_str}) VALUES ({placeholders})"
        
        # Выполнение пакетной вставки
        for i, values in enumerate(values_list):
            params = dict(zip(columns, values))
            session.execute(text(query), params)
            
            # Коммит каждые 1000 записей или в конце списка
            if (i + 1) % 1000 == 0 or i == len(values_list) - 1:
                session.commit()
                logger.info(f"Вставлено {i + 1} записей в таблицу {table_name}")
        
        return len(values_list)
    except Exception as e:
        session.rollback()
        logger.error(f"Ошибка при пакетной вставке в таблицу {table_name}: {str(e)}")
        raise

# Пример использования функций
if __name__ == "__main__":
    try:
        # Пример SELECT-запроса
        users = execute_select_query(
            "SELECT user_id, username FROM users WHERE status = :status",
            {"status": "active"}
        )
        
        # Пример DML-запроса
        affected = execute_dml_query(
            "UPDATE users SET last_login = SYSDATE WHERE user_id = :user_id",
            {"user_id": 123}
        )
        
        # Пример пакетной вставки
        data = [
            (1, "User 1", "<EMAIL>"),
            (2, "User 2", "<EMAIL>"),
            (3, "User 3", "<EMAIL>")
        ]
        batch_insert("users", ["user_id", "username", "email"], data)
        
    except Exception as e:
        logger.error(f"Произошла ошибка: {str(e)}")
    finally:
        # Закрытие сессии в конце работы
        session.close()
```

## Важные замечания

1. Соединение с базой данных уже настроено в файле `config_db.py` с оптимальными параметрами для пула соединений и DRCP.
2. Не создавайте новые соединения с базой данных, используйте существующие объекты `session` и `engine`.
3. Для долгоживущих приложений периодически проверяйте состояние соединений.
4. При возникновении проблем с соединением проверьте логи в директории `logs/`.
5. Пул соединений настроен на автоматическую проверку соединений перед использованием (`pool_pre_ping=True`).

## Диагностика проблем с соединением

Если возникают проблемы с соединением, выполните следующие шаги:

1. Проверьте логи в файле `logs/database_config.log`
2. Убедитесь, что переменные окружения для подключения к базе данных корректно настроены
3. Проверьте статус DRCP на сервере Oracle:
   ```sql
   SELECT * FROM DBA_CPOOL_INFO;
   ```
4. Проверьте доступность сервера Oracle:
   ```bash
   ping <ORACLE_HOST>
   ```
5. Проверьте количество активных соединений:
   ```sql
   SELECT COUNT(*) FROM V$SESSION WHERE USERNAME = '<ORACLE_USER>';
   ```

## Заключение

Используйте настроенное соединение с базой данных Oracle для всех операций с базой данных в вашем проекте. Это обеспечит эффективное использование ресурсов, повысит производительность и надежность вашего приложения.
