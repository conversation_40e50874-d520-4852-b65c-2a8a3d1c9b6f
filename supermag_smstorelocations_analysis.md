# Анализ таблицы supermag.smstorelocations

*Дата анализа: 2025-05-26 15:47:11*

## Основная информация


## Столбцы

| Имя | Тип данных | Nullable | По умолчанию |
|-----|------------|----------|-------------|
| id | NUMBER | NOT NULL |  |
| name | VARCHAR(255) | NOT NULL |  |
| idclass | NUMBER | NOT NULL |  |
| accepted | CHAR(1) | NOT NULL |  |
| prty | NUMBER | NOT NULL |  |
| flags | NUMBER | NOT NULL |  |
| loctype | NUMBER | NOT NULL |  |
| parentloc | NUMBER | NULL |  |
| rgnid | NUMBER | NOT NULL |  |
| address | VARCHAR(255) | NULL |  |
| tel | VARCHAR(40) | NULL |  |
| fax | VARCHAR(40) | NULL |  |
| commentary | VARCHAR(255) | NULL |  |
| orderalg | VARCHAR(255) | NOT NULL |  |
| cardtype | NUMBER | NULL |  |
| formatid | NUMBER | NULL |  |
| pricingmethod | NUMBER | NOT NULL |  |
| suggestorderalg | VARCHAR(50) | NOT NULL |  |
| floorspace | NUMBER | NULL |  |
| gln | VARCHAR(13) | NULL |  |
| shortname | VARCHAR(255) | NULL |  |
| spoilageloc | NUMBER | NULL |  |
| closededitdate | DATE | NULL |  |
| kpp | VARCHAR(9) | NULL |  |
| closededitdocid | VARCHAR(50) | NULL |  |
| closededitdateold | DATE | NULL |  |
| closededitdocidold | VARCHAR(50) | NULL |  |

## Ограничения

### Первичный ключ

- **smcstorelocations_pk:** (id)

### Внешний ключ

- **smcparentloc:** (parentloc) -> supermag.
- **smcspoilageloc:** (spoilageloc) -> supermag.
- **smcstoreclass:** (idclass) -> supermag.
- **smcstorelocations_cardtype:** (cardtype) -> supermag.
- **smcstorelocations_formatid:** (formatid) -> supermag.
- **smcstorelocations_ordalg:** (orderalg) -> supermag.
- **smcstorelocations_rgn:** (rgnid) -> supermag.
- **smcstorelocations_suggordalg:** (suggestorderalg) -> supermag.

### Уникальный ключ

- **smcstorelocations_name:** (name)

## Индексы

| Имя | Тип | Уникальность | Столбцы |
|-----|-----|--------------|--------|
| smcstorelocations_name | NORMAL | UNIQUE | name ASC |
| smstorelocations_cardtypeidx | NORMAL | NONUNIQUE | cardtype ASC |
| smstorelocations_classidx | NORMAL | NONUNIQUE | idclass ASC |
| smstorelocations_formatidx | NORMAL | NONUNIQUE | formatid ASC |
| smstorelocations_parentidx | NORMAL | NONUNIQUE | parentloc ASC |
| smstorelocations_rgnidx | NORMAL | NONUNIQUE | rgnid ASC |
| smstorelocations_spoilageidx | NORMAL | NONUNIQUE | spoilageloc ASC |

## Зависимости

### Ссылается на

- **parentloc** -> smstorelocations.id (через smcparentloc)
- **spoilageloc** -> smstorelocations.id (через smcspoilageloc)
- **idclass** -> sastoreclass.id (через smcstoreclass)
- **cardtype** -> smdisctype.id (через smcstorelocations_cardtype)
- **formatid** -> sastoreformats.id (через smcstorelocations_formatid)
- **orderalg** -> ssgenorderalg.algproc (через smcstorelocations_ordalg)
- **rgnid** -> smregions.rgnid (через smcstorelocations_rgn)
- **suggestorderalg** -> sssuggestorderalg.id (через smcstorelocations_suggordalg)

