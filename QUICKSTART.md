# 🚀 Быстрый старт Database Explorer MCP

Это руководство поможет вам быстро настроить и запустить Database Explorer MCP Server для работы с ИИ агентами.

## 📋 Предварительные требования

- Python 3.8+
- Доступ к базе данных Oracle (или другой поддерживаемой СУБД)
- <PERSON> (для интеграции с ИИ)

## ⚡ Быстрая установка

### 1. Установка зависимостей

```bash
pip install -r requirements_mcp.txt
```

### 2. Настройка подключения к БД

Создайте файл `.env` из примера:

```bash
cp .env.example .env
```

Отредактируйте `.env`:

```env
DB_TYPE=oracle
DB_HOST=your_host
DB_PORT=1521
DB_USER=your_username
DB_PASSWORD=your_password
DB_SERVICE=your_service_name
```

### 3. Тестирование

```bash
python test_mcp_server.py
```

Выберите опцию "2" для тестирования списка инструментов.

### 4. Установка в Claude Desktop

```bash
python install_mcp.py
```

Следуйте инструкциям установщика.

### 5. Перезапуск Claude Desktop

Перезапустите Claude Desktop для применения изменений.

## 🎯 Первые шаги

После установки в Claude Desktop вы можете использовать следующие команды:

### Подключение к БД
```
Подключись к базе данных Oracle
```

### Исследование схем
```
Покажи список всех схем в базе данных
```

### Анализ таблиц
```
Получи структуру таблицы TABLE_NAME в схеме SCHEMA_NAME
```

### Выполнение запросов
```
Выполни запрос: SELECT * FROM DUAL
```

## 🔧 Доступные инструменты

1. **connect_database** - Подключение к БД
2. **list_schemas** - Список схем
3. **get_table_structure** - Структура таблицы
4. **execute_safe_query** - Безопасные запросы
5. **get_table_sample_data** - Образцы данных
6. И еще 10 инструментов...

## 📚 Дополнительная документация

- [README_MCP.md](README_MCP.md) - Полная документация MCP сервера
- [README.md](README.md) - Общая документация проекта

## 🆘 Решение проблем

### Ошибка подключения к БД
- Проверьте настройки в `.env` файле
- Убедитесь, что БД доступна
- Проверьте логи в `logs/mcp_server.log`

### MCP сервер не запускается
- Убедитесь, что установлены все зависимости
- Проверьте версию Python (требуется 3.8+)
- Запустите `python test_mcp_server.py` для диагностики

### Claude Desktop не видит сервер
- Перезапустите Claude Desktop
- Проверьте конфигурацию в `~/.config/Claude/claude_desktop_config.json`
- Убедитесь, что путь к `mcp_server.py` корректный

## 💡 Советы

1. **Используйте переменные окружения** для безопасного хранения паролей
2. **Тестируйте подключение** перед установкой в Claude Desktop
3. **Изучите логи** при возникновении проблем
4. **Начните с простых запросов** для знакомства с инструментами

## 🎉 Готово!

Теперь вы можете использовать Database Explorer MCP для исследования структуры баз данных с помощью ИИ агентов!
