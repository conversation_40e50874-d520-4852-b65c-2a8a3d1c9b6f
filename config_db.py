from dotenv import load_dotenv
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
import os
import sys
import oracledb
from loguru import logger

# Настройка логирования для этого модуля
logger.add(
    "logs/database_config.log",
    rotation="100 MB",
    retention="30 days",
    format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {function}:{line} | {message}",
    level="INFO",
)

sys.path.append(os.path.join(sys.path[0], "db_connections"))

load_dotenv()

ORACLE_PORT = os.environ.get("ORACLE_PORT")
ORACLE_USER = os.environ.get("ORACLE_USER")
ORACLE_PASS = os.environ.get("ORACLE_PASS")
ORACLE_HOST = os.environ.get("ORACLE_HOST")
ORACLE_BASE = os.environ.get("ORACLE_BASE")

# Проверяем наличие всех необходимых переменных окружения для Oracle
required_oracle_vars = [
    "ORACLE_PORT",
    "ORACLE_USER",
    "ORACLE_PASS",
    "ORACLE_HOST",
    "ORACLE_BASE",
]
for var in required_oracle_vars:
    if not os.environ.get(var):
        logger.error(f"Отсутствует обязательная переменная окружения для Oracle: {var}")
        raise ValueError(f"Missing required Oracle environment variable: {var}")

# URL для подключения к Oracle
url = f"oracle+oracledb://{ORACLE_USER}:{ORACLE_PASS}@{ORACLE_HOST}:{ORACLE_PORT}/{ORACLE_BASE}"

logger.info(
    f"Подключение к базе данных Oracle с DRCP: {ORACLE_HOST}:{ORACLE_PORT}/{ORACLE_BASE}"
)

try:
    # Настройка пула соединений с поддержкой DRCP
    engine = create_engine(
        url,
        pool_size=5,  # Максимальное количество постоянных соединений
        max_overflow=10,  # Максимальное количество временных соединений
        pool_timeout=30,  # Время ожидания доступного соединения (в секундах)
        pool_recycle=1800,  # Время жизни соединения (в секундах, 30 минут)
        pool_pre_ping=True,  # Проверка соединения перед использованием
        echo_pool=True,  # Логирование операций пула соединений
        connect_args={
            "cclass": "ORACLE_MCP_APP",  # Имя класса соединений для DRCP
            "purity": oracledb.PURITY_SELF,  # Использовать существующую сессию
        },
    )

    # Создание фабрики сессий
    Session = sessionmaker(bind=engine)

    # Создание глобальной сессии (для обратной совместимости)
    session = Session()

    # Функция для создания новой сессии (для потокобезопасности)
    def create_session():
        """
        Создает и возвращает новую сессию SQLAlchemy.

        Эта функция используется для обеспечения потокобезопасности при параллельном выполнении запросов.
        Каждый поток должен использовать свою собственную сессию.

        Returns:
            SQLAlchemy Session: Новая сессия SQLAlchemy.
        """
        return Session()

    # Информация о пуле соединений
    pool_info = {
        "Размер пула": engine.pool.size(),
        "Максимальный размер пула": engine.pool.size() + engine.pool._max_overflow,
        "Время жизни соединения": f"{engine.pool._recycle} секунд",
        "Использование DRCP": "Да",
        "Класс соединений DRCP": "ORACLE_MCP_APP",
        "Purity": "SELF",
    }

    logger.info(
        f"Успешное подключение к базе данных Oracle с настроенным пулом соединений"
    )
    for key, value in pool_info.items():
        logger.info(f"{key}: {value}")
except Exception as e:
    logger.error(f"Ошибка подключения к базе данных Oracle: {str(e)}")
    raise

# Инициализация переменной qdrant_client как None для обратной совместимости
qdrant_client = None
