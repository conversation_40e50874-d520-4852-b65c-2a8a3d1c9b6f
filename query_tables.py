#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Скрипт для анализа структуры таблиц SMCARD, SVCARDTREESPLIT и SMSTORELOCATIONS.
"""

import os
import sys
from loguru import logger
from sqlalchemy import text
from tabulate import tabulate

# Настройка логирования
logger.add(
    "logs/tables_analysis.log",
    rotation="100 MB",
    retention="30 days",
    format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {function}:{line} | {message}",
    level="INFO"
)

def execute_select_query(query, params=None):
    """
    Выполнение SELECT-запроса с обработкой ошибок.
    """
    try:
        # Импортируем функцию создания сессии
        from config_db import create_session

        # Создаем новую сессию для каждого запроса
        with create_session() as session:
            result = session.execute(text(query), params or {})
            return result.fetchall()
    except Exception as e:
        logger.error(f"Ошибка при выполнении SELECT-запроса: {str(e)}")
        logger.error(f"Запрос: {query}")
        logger.error(f"Параметры: {params}")
        return []

def get_table_structure(table_name, schema_name="SUPERMAG"):
    """Получение структуры таблицы."""
    query = """
    SELECT column_name, data_type, data_length, nullable
    FROM all_tab_columns
    WHERE table_name = :table
    AND owner = :schema
    ORDER BY column_id
    """
    result = execute_select_query(query, {"table": table_name, "schema": schema_name})
    
    # Преобразование результата в список словарей
    columns_info = []
    for row in result:
        if isinstance(row, tuple):
            columns_info.append({
                "column_name": row[0],
                "data_type": row[1],
                "data_length": row[2],
                "nullable": row[3]
            })
        else:
            columns_info.append({
                "column_name": row.column_name,
                "data_type": row.data_type,
                "data_length": row.data_length,
                "nullable": row.nullable
            })
    
    return columns_info

def get_table_constraints(table_name, schema_name="SUPERMAG"):
    """Получение ограничений таблицы."""
    query = """
    SELECT c.constraint_name, c.constraint_type, cc.column_name
    FROM all_constraints c
    JOIN all_cons_columns cc ON c.constraint_name = cc.constraint_name
    WHERE c.table_name = :table
    AND c.owner = :schema
    ORDER BY c.constraint_type, cc.position
    """
    result = execute_select_query(query, {"table": table_name, "schema": schema_name})
    
    # Преобразование результата в список словарей
    constraints_info = []
    for row in result:
        if isinstance(row, tuple):
            constraints_info.append({
                "constraint_name": row[0],
                "constraint_type": row[1],
                "column_name": row[2]
            })
        else:
            constraints_info.append({
                "constraint_name": row.constraint_name,
                "constraint_type": row.constraint_type,
                "column_name": row.column_name
            })
    
    return constraints_info

def get_table_relationships(table_name, schema_name="SUPERMAG"):
    """Получение связей таблицы с другими таблицами."""
    query = """
    -- Получение внешних ключей, где таблица ссылается на другие
    SELECT a.table_name as referenced_table,
           a.constraint_name as fk_constraint,
           acc.column_name as fk_column,
           r.table_name as referenced_by_table,
           rc.column_name as referenced_column,
           'outgoing' as dependency_type
    FROM all_constraints a
    JOIN all_cons_columns acc ON a.constraint_name = acc.constraint_name AND a.owner = acc.owner
    JOIN all_constraints r ON a.r_constraint_name = r.constraint_name
    JOIN all_cons_columns rc ON r.constraint_name = rc.constraint_name AND r.owner = rc.owner
    WHERE a.constraint_type = 'R'
    AND a.table_name = :table
    AND a.owner = :schema

    UNION ALL

    -- Получение внешних ключей, где на таблицу ссылаются другие
    SELECT a.table_name as referenced_table,
           a.constraint_name as fk_constraint,
           acc.column_name as fk_column,
           r.table_name as referenced_by_table,
           rc.column_name as referenced_column,
           'incoming' as dependency_type
    FROM all_constraints a
    JOIN all_cons_columns acc ON a.constraint_name = acc.constraint_name AND a.owner = acc.owner
    JOIN all_constraints r ON a.r_constraint_name = r.constraint_name
    JOIN all_cons_columns rc ON r.constraint_name = rc.constraint_name AND r.owner = rc.owner
    WHERE a.constraint_type = 'R'
    AND r.table_name = :table
    AND r.owner = :schema

    ORDER BY dependency_type, referenced_table
    """
    result = execute_select_query(query, {"table": table_name, "schema": schema_name})
    
    # Преобразование результата в список словарей
    relationships_info = []
    for row in result:
        if isinstance(row, tuple):
            relationships_info.append({
                "referenced_table": row[0],
                "fk_constraint": row[1],
                "fk_column": row[2],
                "referenced_by_table": row[3],
                "referenced_column": row[4],
                "dependency_type": row[5]
            })
        else:
            relationships_info.append({
                "referenced_table": row.referenced_table,
                "fk_constraint": row.fk_constraint,
                "fk_column": row.fk_column,
                "referenced_by_table": row.referenced_by_table,
                "referenced_column": row.referenced_column,
                "dependency_type": row.dependency_type
            })
    
    return relationships_info

def get_table_data_sample(table_name, schema_name="SUPERMAG", limit=10):
    """Получение образца данных из таблицы."""
    query = f"""
    SELECT * FROM {schema_name}.{table_name}
    WHERE ROWNUM <= :limit
    """
    result = execute_select_query(query, {"limit": limit})
    return result

def create_markdown_report(table_name, columns_info, constraints_info, relationships_info, data_sample=None):
    """Создание markdown-отчета о структуре таблицы."""
    try:
        with open(f"{table_name.lower()}_analysis.md", "w", encoding="utf-8") as f:
            f.write(f"# Анализ таблицы {table_name}\n\n")
            
            # Структура таблицы
            f.write("## Структура таблицы\n\n")
            if columns_info:
                f.write("| Имя столбца | Тип данных | Длина | Nullable |\n")
                f.write("|-------------|------------|-------|----------|\n")
                for col in columns_info:
                    f.write(f"| {col['column_name']} | {col['data_type']} | {col['data_length']} | {col['nullable']} |\n")
            else:
                f.write("Не удалось получить информацию о структуре таблицы.\n")
            
            # Ограничения таблицы
            f.write("\n## Ограничения таблицы\n\n")
            if constraints_info:
                f.write("| Имя ограничения | Тип ограничения | Столбец |\n")
                f.write("|-----------------|-----------------|--------|\n")
                for con in constraints_info:
                    f.write(f"| {con['constraint_name']} | {con['constraint_type']} | {con['column_name']} |\n")
            else:
                f.write("Не удалось получить информацию об ограничениях таблицы.\n")
            
            # Связи таблицы
            f.write("\n## Связи с другими таблицами\n\n")
            if relationships_info:
                f.write("| Таблица | FK | FK столбец | Связанная таблица | Связанный столбец | Тип связи |\n")
                f.write("|---------|----|-----------|--------------------|------------------|----------|\n")
                for rel in relationships_info:
                    f.write(f"| {rel['referenced_table']} | {rel['fk_constraint']} | {rel['fk_column']} | ")
                    f.write(f"{rel['referenced_by_table']} | {rel['referenced_column']} | {rel['dependency_type']} |\n")
            else:
                f.write("Не удалось получить информацию о связях таблицы или таблица не имеет внешних ключей.\n")
            
            logger.info(f"Markdown-файл с результатами анализа успешно создан: {table_name.lower()}_analysis.md")
    except Exception as e:
        logger.error(f"Ошибка при создании markdown-файла: {str(e)}")

def main():
    """Основная функция для анализа таблиц."""
    logger.info("Начало анализа таблиц")
    
    tables = ["SMCARD", "SVCARDTREESPLIT", "SMSTORELOCATIONS"]
    
    for table_name in tables:
        logger.info(f"Анализ таблицы {table_name}")
        
        # Получение структуры таблицы
        columns_info = get_table_structure(table_name)
        print(f"\n=== Структура таблицы {table_name} ===")
        if columns_info:
            headers = ["Имя столбца", "Тип данных", "Длина", "Nullable"]
            table_data = [
                (col["column_name"], col["data_type"], col["data_length"], col["nullable"])
                for col in columns_info
            ]
            print(tabulate(table_data, headers=headers, tablefmt="grid"))
        else:
            print(f"Не удалось получить информацию о структуре таблицы {table_name}")
        
        # Получение ограничений таблицы
        constraints_info = get_table_constraints(table_name)
        print(f"\n=== Ограничения таблицы {table_name} ===")
        if constraints_info:
            headers = ["Имя ограничения", "Тип ограничения", "Столбец"]
            table_data = [
                (con["constraint_name"], con["constraint_type"], con["column_name"])
                for con in constraints_info
            ]
            print(tabulate(table_data, headers=headers, tablefmt="grid"))
        else:
            print(f"Не удалось получить информацию об ограничениях таблицы {table_name}")
        
        # Получение связей таблицы
        relationships_info = get_table_relationships(table_name)
        print(f"\n=== Связи таблицы {table_name} с другими таблицами ===")
        if relationships_info:
            headers = [
                "Таблица",
                "FK",
                "FK столбец",
                "Связанная таблица",
                "Связанный столбец",
                "Тип связи",
            ]
            table_data = [
                (
                    rel["referenced_table"],
                    rel["fk_constraint"],
                    rel["fk_column"],
                    rel["referenced_by_table"],
                    rel["referenced_column"],
                    rel["dependency_type"],
                )
                for rel in relationships_info
            ]
            print(tabulate(table_data, headers=headers, tablefmt="grid"))
        else:
            print(f"Не удалось получить информацию о связях таблицы {table_name}")
        
        # Создание markdown-отчета
        create_markdown_report(table_name, columns_info, constraints_info, relationships_info)
    
    logger.info("Анализ таблиц завершен")

if __name__ == "__main__":
    main()
