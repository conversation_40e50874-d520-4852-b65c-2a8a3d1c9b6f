# 🔗 Руководство по интеграции Database Explorer MCP

Полное руководство по интеграции Database Explorer MCP Server с различными ИИ-редакторами кода.

## 📋 Содержание

1. [Общие требования](#общие-требования)
2. [<PERSON>](#claude-desktop)
3. [Cursor IDE](#cursor-ide)
4. [Augment Code](#augment-code)
5. [VS Code с Continue](#vs-code-с-continue)
6. [Windsurf](#windsurf)
7. [Универсальная интеграция](#универсальная-интеграция)
8. [Тестирование](#тестирование)
9. [Устранение неполадок](#устранение-неполадок)

## 🔧 Общие требования

### Предварительные условия
- ✅ Python 3.8+
- ✅ Database Explorer MCP Server
- ✅ Доступ к базе данных
- ✅ Настроенные переменные окружения

### Проверка готовности
```bash
# Тестирование MCP сервера
python test_mcp_server.py

# Проверка переменных окружения
python -c "import os; print('DB_TYPE:', os.getenv('DB_TYPE'))"
```

## 🤖 Claude Desktop

### Автоматическая установка
```bash
python install_mcp.py
```

### Ручная настройка

1. **Найдите конфигурационный файл:**
   - Windows: `%APPDATA%\Claude\claude_desktop_config.json`
   - macOS: `~/Library/Application Support/Claude/claude_desktop_config.json`
   - Linux: `~/.config/Claude/claude_desktop_config.json`

2. **Добавьте конфигурацию:**
```json
{
  "mcpServers": {
    "database-explorer": {
      "command": "python",
      "args": ["path/to/mcp_server.py"],
      "env": {
        "DB_TYPE": "oracle",
        "DB_HOST": "your_host",
        "DB_PORT": "1521",
        "DB_USER": "your_user",
        "DB_PASSWORD": "your_password",
        "DB_SERVICE": "your_service"
      }
    }
  }
}
```

3. **Перезапустите Claude Desktop**

### Использование в Claude Desktop
```
Подключись к базе данных Oracle
Покажи все схемы в базе данных
Получи структуру таблицы USERS в схеме MAIN
```

## 🎯 Cursor IDE

### Автоматическая установка
```bash
python integrations/cursor/install_cursor.py
```

### Проверка поддержки MCP
```bash
python integrations/cursor/check_mcp_support.py
```

### Ручная настройка

1. **Откройте настройки Cursor** (Ctrl+,)
2. **Найдите раздел MCP** или Extensions
3. **Добавьте конфигурацию:**
```json
{
  "mcp.servers": {
    "database-explorer": {
      "command": "python",
      "args": ["path/to/mcp_server.py"],
      "env": {
        "DB_TYPE": "oracle"
      }
    }
  },
  "mcp.autoStart": true
}
```

### Использование в Cursor
```
@database-explorer подключись к базе данных
@database-explorer покажи схемы
@database-explorer анализируй таблицу PRODUCTS
```

## 🎨 Augment Code

### Автоматическая установка
```bash
python integrations/augment/install_augment.py
```

### Ручная настройка

1. **Создайте конфигурационный файл:**
   - Windows: `%APPDATA%\Augment\mcp_config.json`
   - macOS: `~/Library/Application Support/Augment/mcp_config.json`
   - Linux: `~/.config/Augment/mcp_config.json`

2. **Добавьте конфигурацию:**
```json
{
  "mcpServers": {
    "database-explorer": {
      "name": "Database Explorer",
      "command": "python",
      "args": ["path/to/mcp_server.py"],
      "env": {
        "DB_TYPE": "oracle"
      },
      "capabilities": {
        "tools": true
      },
      "settings": {
        "autoStart": true,
        "logLevel": "info"
      }
    }
  }
}
```

### Использование в Augment Code
```
Исследуй структуру базы данных
Найди все таблицы в схеме INVENTORY
Покажи связи между таблицами
```

## 💻 VS Code с Continue

### Установка Continue
1. Откройте VS Code
2. Установите расширение "Continue"
3. Настройте Continue

### Настройка MCP
1. **Откройте настройки Continue** (Ctrl+Shift+P → "Continue: Open Config")
2. **Добавьте MCP конфигурацию:**
```json
{
  "mcpServers": {
    "database-explorer": {
      "command": "python",
      "args": ["path/to/mcp_server.py"],
      "env": {
        "DB_TYPE": "oracle"
      }
    }
  }
}
```

### Использование в VS Code
```
Подключись к базе данных через MCP
Анализируй схему базы данных
Создай диаграмму связей таблиц
```

## 🌊 Windsurf

### Настройка
1. **Откройте настройки Windsurf**
2. **Найдите раздел MCP/Tools**
3. **Добавьте Database Explorer:**
```json
{
  "tools": {
    "database-explorer": {
      "type": "mcp",
      "command": "python",
      "args": ["path/to/mcp_server.py"],
      "description": "Database structure exploration"
    }
  }
}
```

## 🔄 Универсальная интеграция

### Автоматический установщик
```bash
python integrations/universal_installer.py
```

Этот скрипт:
- ✅ Определяет установленные редакторы
- ✅ Предлагает варианты установки
- ✅ Настраивает конфигурацию автоматически
- ✅ Создает примеры использования

### Поддерживаемые редакторы
- ✅ Claude Desktop
- ✅ Cursor IDE
- ✅ Augment Code
- ✅ VS Code с Continue
- ✅ Windsurf
- 🚧 Другие MCP-совместимые редакторы

## 🧪 Тестирование

### Комплексное тестирование
```bash
python integrations/test_integration.py
```

### Индивидуальные тесты
```bash
# Тест MCP сервера
python test_mcp_server.py

# Тест конфигурации Claude
python integrations/test_integration.py

# Проверка переменных окружения
python -c "import os; print([k for k in os.environ if k.startswith('DB_')])"
```

### Проверка подключения
```bash
# Через MCP клиент
python -c "
import asyncio
from mcp.client.session import ClientSession
from mcp.client.stdio import StdioServerParameters, stdio_client

async def test():
    params = StdioServerParameters(command='python', args=['mcp_server.py'])
    async with stdio_client(params) as (read, write):
        async with ClientSession(read, write) as session:
            await session.initialize()
            tools = await session.list_tools()
            print(f'Доступно {len(tools.tools)} инструментов')

asyncio.run(test())
"
```

## 🔧 Устранение неполадок

### Общие проблемы

#### MCP сервер не запускается
```bash
# Проверка зависимостей
pip install -r requirements_mcp.txt

# Проверка Python версии
python --version

# Тест запуска
python mcp_server.py --help
```

#### Редактор не видит MCP сервер
1. **Проверьте путь к серверу** в конфигурации
2. **Перезапустите редактор**
3. **Проверьте логи** редактора
4. **Убедитесь в правильности JSON** конфигурации

#### Ошибки подключения к БД
```bash
# Проверка переменных окружения
cat .env

# Тест подключения
python -c "
from mcp_tools.database_tools import DatabaseTools
db = DatabaseTools()
result = db.connect_database('oracle')
print(result)
"
```

### Специфичные проблемы

#### Claude Desktop
- **Проблема:** Сервер не отображается
- **Решение:** Проверьте `claude_desktop_config.json` на валидность JSON

#### Cursor IDE
- **Проблема:** MCP не поддерживается
- **Решение:** Обновите Cursor до последней версии или используйте альтернативный способ

#### Augment Code
- **Проблема:** Конфигурация не применяется
- **Решение:** Убедитесь, что файл `mcp_config.json` в правильной директории

### Логи и диагностика

#### Включение подробного логирования
```bash
# В .env файле
LOG_LEVEL=DEBUG

# Запуск с логированием
python mcp_server.py 2>&1 | tee mcp_debug.log
```

#### Проверка логов
```bash
# Логи MCP сервера
tail -f logs/mcp_server.log

# Логи редактора (пример для Claude)
# Windows: %APPDATA%\Claude\logs
# macOS: ~/Library/Logs/Claude
# Linux: ~/.local/share/Claude/logs
```

## 📞 Поддержка

При возникновении проблем:

1. **Запустите диагностику:**
   ```bash
   python integrations/test_integration.py
   ```

2. **Проверьте документацию** конкретного редактора

3. **Создайте issue** в репозитории проекта с:
   - Версией Python
   - Типом ОС
   - Используемым редактором
   - Логами ошибок
   - Результатами тестирования

## 🎉 Успешная интеграция

После успешной настройки вы сможете:

- 🔍 **Исследовать структуру БД** через ИИ
- 📊 **Анализировать таблицы и связи**
- 🔒 **Выполнять безопасные запросы**
- 📈 **Получать статистику и метаданные**
- 🤖 **Использовать 15 специализированных инструментов**

Наслаждайтесь работой с Database Explorer MCP! 🚀
