#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
MCP сервер для исследования структуры реляционных баз данных.

Предоставляет инструменты для ИИ агентов для анализа структуры баз данных,
включая схемы, таблицы, столбцы, ограничения, индексы и зависимости.
"""

import os
import asyncio
from typing import Dict, Any, Optional, List
from loguru import logger
from mcp.server.fastmcp import FastMCP
from mcp.server.fastmcp import Context

from mcp_tools.database_tools import DatabaseTools
from mcp_tools.schema_tools import SchemaTools
from mcp_tools.table_tools import TableTools

# Настройка логирования
os.makedirs("logs", exist_ok=True)
logger.add(
    "logs/mcp_server.log",
    rotation="100 MB",
    retention="30 days",
    format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {function}:{line} | {message}",
    level="INFO",
)

# Создание MCP сервера
mcp = FastMCP("Database Explorer MCP Server")

# Инициализация инструментов
database_tools = DatabaseTools()
schema_tools = SchemaTools(database_tools)
table_tools = TableTools(database_tools)


@mcp.tool()
def connect_database(
    db_type: str,
    host: Optional[str] = None,
    port: Optional[int] = None,
    user: Optional[str] = None,
    password: Optional[str] = None,
    service: Optional[str] = None,
    database: Optional[str] = None
) -> Dict[str, Any]:
    """
    Подключается к базе данных с указанными параметрами.
    
    Args:
        db_type: Тип базы данных (oracle, mysql, postgresql, clickhouse)
        host: Хост базы данных (опционально, можно использовать переменную окружения DB_HOST)
        port: Порт базы данных (опционально, будет использован порт по умолчанию)
        user: Имя пользователя (опционально, можно использовать переменную окружения DB_USER)
        password: Пароль (опционально, можно использовать переменную окружения DB_PASSWORD)
        service: Имя сервиса для Oracle (опционально, можно использовать переменную окружения DB_SERVICE)
        database: Имя базы данных для MySQL/PostgreSQL (опционально, можно использовать переменную окружения DB_NAME)
        
    Returns:
        Результат подключения с информацией о статусе и параметрах соединения
    """
    logger.info(f"Попытка подключения к базе данных типа: {db_type}")
    return database_tools.connect_database(db_type, host, port, user, password, service, database)


@mcp.tool()
def disconnect_database() -> Dict[str, Any]:
    """
    Отключается от базы данных.
    
    Returns:
        Результат отключения
    """
    logger.info("Отключение от базы данных")
    return database_tools.disconnect_database()


@mcp.tool()
def get_connection_status() -> Dict[str, Any]:
    """
    Получает статус подключения к базе данных.
    
    Returns:
        Статус подключения с информацией о текущем соединении
    """
    return database_tools.get_connection_status()


@mcp.tool()
def execute_safe_query(query: str, limit: int = 100) -> Dict[str, Any]:
    """
    Выполняет безопасный SELECT запрос с ограничениями.
    
    Args:
        query: SQL запрос (только SELECT запросы разрешены)
        limit: Максимальное количество возвращаемых строк (максимум 1000)
        
    Returns:
        Результат выполнения запроса с данными
    """
    logger.info(f"Выполнение безопасного запроса с лимитом {limit}")
    return database_tools.execute_safe_query(query, limit)


@mcp.tool()
def list_schemas() -> Dict[str, Any]:
    """
    Получает список всех доступных схем в базе данных.
    
    Returns:
        Список схем с информацией о количестве таблиц в каждой схеме
    """
    logger.info("Получение списка схем")
    return schema_tools.list_schemas()


@mcp.tool()
def get_schema_info(schema_name: Optional[str] = None) -> Dict[str, Any]:
    """
    Получает детальную информацию о схеме.
    
    Args:
        schema_name: Имя схемы (если не указано, используется текущая схема)
        
    Returns:
        Детальная информация о схеме включая статистику по таблицам
    """
    logger.info(f"Получение информации о схеме: {schema_name or 'текущая'}")
    return schema_tools.get_schema_info(schema_name)


@mcp.tool()
def list_tables_in_schema(schema_name: Optional[str] = None) -> Dict[str, Any]:
    """
    Получает список таблиц в указанной схеме.
    
    Args:
        schema_name: Имя схемы (если не указано, используется текущая схема)
        
    Returns:
        Список таблиц в схеме с базовой информацией о каждой таблице
    """
    logger.info(f"Получение списка таблиц в схеме: {schema_name or 'текущая'}")
    return schema_tools.list_tables_in_schema(schema_name)


@mcp.tool()
def compare_schemas(schema1: str, schema2: str) -> Dict[str, Any]:
    """
    Сравнивает две схемы и находит различия.
    
    Args:
        schema1: Имя первой схемы
        schema2: Имя второй схемы
        
    Returns:
        Результат сравнения схем с информацией о различиях
    """
    logger.info(f"Сравнение схем: {schema1} и {schema2}")
    return schema_tools.compare_schemas(schema1, schema2)


@mcp.tool()
def get_table_structure(table_name: str, schema_name: Optional[str] = None) -> Dict[str, Any]:
    """
    Получает полную структуру таблицы.
    
    Args:
        table_name: Имя таблицы
        schema_name: Имя схемы (если не указано, используется текущая схема)
        
    Returns:
        Полная структура таблицы включая столбцы, ограничения, индексы и зависимости
    """
    logger.info(f"Получение структуры таблицы: {schema_name or 'текущая'}.{table_name}")
    return table_tools.get_table_structure(table_name, schema_name)


@mcp.tool()
def get_table_columns(table_name: str, schema_name: Optional[str] = None) -> Dict[str, Any]:
    """
    Получает информацию о столбцах таблицы.
    
    Args:
        table_name: Имя таблицы
        schema_name: Имя схемы (если не указано, используется текущая схема)
        
    Returns:
        Детальная информация о столбцах таблицы включая типы данных и ограничения
    """
    logger.info(f"Получение информации о столбцах таблицы: {schema_name or 'текущая'}.{table_name}")
    return table_tools.get_table_columns(table_name, schema_name)


@mcp.tool()
def get_table_constraints(table_name: str, schema_name: Optional[str] = None) -> Dict[str, Any]:
    """
    Получает информацию об ограничениях таблицы.
    
    Args:
        table_name: Имя таблицы
        schema_name: Имя схемы (если не указано, используется текущая схема)
        
    Returns:
        Информация об ограничениях таблицы сгруппированная по типам
    """
    logger.info(f"Получение ограничений таблицы: {schema_name or 'текущая'}.{table_name}")
    return table_tools.get_table_constraints(table_name, schema_name)


@mcp.tool()
def get_table_indexes(table_name: str, schema_name: Optional[str] = None) -> Dict[str, Any]:
    """
    Получает информацию об индексах таблицы.
    
    Args:
        table_name: Имя таблицы
        schema_name: Имя схемы (если не указано, используется текущая схема)
        
    Returns:
        Информация об индексах таблицы сгруппированная по типам
    """
    logger.info(f"Получение индексов таблицы: {schema_name or 'текущая'}.{table_name}")
    return table_tools.get_table_indexes(table_name, schema_name)


@mcp.tool()
def get_table_dependencies(table_name: str, schema_name: Optional[str] = None) -> Dict[str, Any]:
    """
    Получает информацию о зависимостях таблицы (внешние ключи).
    
    Args:
        table_name: Имя таблицы
        schema_name: Имя схемы (если не указано, используется текущая схема)
        
    Returns:
        Информация о зависимостях таблицы разделенная на входящие и исходящие
    """
    logger.info(f"Получение зависимостей таблицы: {schema_name or 'текущая'}.{table_name}")
    return table_tools.get_table_dependencies(table_name, schema_name)


@mcp.tool()
def get_table_sample_data(table_name: str, schema_name: Optional[str] = None, limit: int = 10) -> Dict[str, Any]:
    """
    Получает образец данных из таблицы.
    
    Args:
        table_name: Имя таблицы
        schema_name: Имя схемы (если не указано, используется текущая схема)
        limit: Количество строк для выборки (максимум 100)
        
    Returns:
        Образец данных из таблицы с информацией о столбцах
    """
    logger.info(f"Получение образца данных из таблицы: {schema_name or 'текущая'}.{table_name}, лимит: {limit}")
    return table_tools.get_table_sample_data(table_name, schema_name, limit)


@mcp.tool()
def analyze_table_relationships(table_name: str, schema_name: Optional[str] = None) -> Dict[str, Any]:
    """
    Анализирует связи таблицы с другими таблицами.
    
    Args:
        table_name: Имя таблицы
        schema_name: Имя схемы (если не указано, используется текущая схема)
        
    Returns:
        Анализ связей таблицы с определением типа таблицы в иерархии
    """
    logger.info(f"Анализ связей таблицы: {schema_name or 'текущая'}.{table_name}")
    return table_tools.analyze_table_relationships(table_name, schema_name)


if __name__ == "__main__":
    logger.info("Запуск MCP сервера для исследования баз данных")
    mcp.run()
