@echo off

echo Testing cleanup batch file...
echo.

echo Current directory: %CD%
echo.

echo Checking for mcp_server.py:
if exist "mcp_server.py" (
    echo [OK] mcp_server.py found
) else (
    echo [ERROR] mcp_server.py NOT found
)

echo.
echo Files to cleanup in current directory:

echo.
echo Analysis files:
for %%f in (ffmaprep_analysis.md supermag_*.md *_analysis.md) do (
    if exist "%%f" echo    - %%f
)

echo.
echo Old configurations:
for %%f in (old_*.md *_prompt.md oracle_connection_prompt.md) do (
    if exist "%%f" echo    - %%f
)

echo.
echo Test files:
for %%f in (my_test.py test_connection_pool.py test_drcp_connection.py test_oracle_connection.py query_*.py) do (
    if exist "%%f" echo    - %%f
)

echo.
echo Folders to cleanup:
if exist "__pycache__" echo    - __pycache__
if exist "logs" echo    - logs
if exist "venv" echo    - venv

echo.
echo Additional files:
for %%f in (_requirements.txt requirements_db_explorer.txt) do (
    if exist "%%f" echo    - %%f
)

echo.
echo Testing completed.
pause
