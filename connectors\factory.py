#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Фабрика для создания коннекторов к различным СУБД.
"""

from typing import Dict, Any, Optional
from loguru import logger

from connectors.base import DatabaseConnector
from connectors.oracle import OracleDatabaseConnector


class DatabaseConnectorFactory:
    """
    Фабрика для создания коннекторов к различным СУБД.
    
    Позволяет создавать коннекторы к различным СУБД на основе типа базы данных.
    """
    
    @staticmethod
    def create_connector(db_type: str, connection_params: Optional[Dict[str, Any]] = None) -> Optional[DatabaseConnector]:
        """
        Создает коннектор к указанной СУБД.
        
        Args:
            db_type: Тип базы данных ('oracle', 'mysql', 'postgresql', 'clickhouse').
            connection_params: Параметры подключения к базе данных.
                Если None, используются параметры из конфигурационного файла.
                
        Returns:
            DatabaseConnector: Коннектор к указанной СУБД или None, если тип не поддерживается.
        """
        db_type = db_type.lower()
        
        if db_type == 'oracle':
            return OracleDatabaseConnector(connection_params)
        elif db_type == 'mysql':
            # TODO: Реализовать коннектор для MySQL
            logger.warning("Коннектор для MySQL пока не реализован")
            return None
        elif db_type == 'postgresql':
            # TODO: Реализовать коннектор для PostgreSQL
            logger.warning("Коннектор для PostgreSQL пока не реализован")
            return None
        elif db_type == 'clickhouse':
            # TODO: Реализовать коннектор для ClickHouse
            logger.warning("Коннектор для ClickHouse пока не реализован")
            return None
        else:
            logger.error(f"Неподдерживаемый тип базы данных: {db_type}")
            return None
