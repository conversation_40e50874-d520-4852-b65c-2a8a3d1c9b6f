#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
MCP инструменты для работы со схемами базы данных.
"""

from typing import Dict, Any, Optional, List
from loguru import logger


class SchemaTools:
    """
    Класс для инструментов работы со схемами базы данных.
    """
    
    def __init__(self, database_tools):
        """
        Инициализация с ссылкой на DatabaseTools.
        
        Args:
            database_tools: Экземпляр DatabaseTools для доступа к коннектору
        """
        self.database_tools = database_tools
    
    @property
    def connector(self):
        """Получает коннектор из database_tools."""
        return self.database_tools.connector
    
    def list_schemas(self) -> Dict[str, Any]:
        """
        Получает список всех доступных схем в базе данных.
        
        Returns:
            Dict[str, Any]: Список схем и информация о них
        """
        try:
            if self.connector is None:
                return {
                    "success": False,
                    "error": "Нет активного соединения с базой данных"
                }
            
            schemas = self.connector.get_schema_names()
            
            if not schemas:
                return {
                    "success": True,
                    "schemas": [],
                    "count": 0,
                    "message": "Схемы не найдены или нет доступа"
                }
            
            # Получаем дополнительную информацию о схемах
            schema_info = []
            for schema in schemas:
                try:
                    tables = self.connector.get_table_names(schema)
                    schema_info.append({
                        "schema_name": schema,
                        "table_count": len(tables),
                        "accessible": True
                    })
                except Exception as e:
                    schema_info.append({
                        "schema_name": schema,
                        "table_count": 0,
                        "accessible": False,
                        "error": str(e)
                    })
            
            return {
                "success": True,
                "schemas": schema_info,
                "count": len(schemas),
                "current_schema": self.connector.get_current_schema(),
                "message": f"Найдено {len(schemas)} схем"
            }
            
        except Exception as e:
            logger.error(f"Ошибка при получении списка схем: {str(e)}")
            return {
                "success": False,
                "error": f"Ошибка при получении списка схем: {str(e)}"
            }
    
    def get_schema_info(self, schema_name: Optional[str] = None) -> Dict[str, Any]:
        """
        Получает детальную информацию о схеме.
        
        Args:
            schema_name: Имя схемы. Если None, используется текущая схема
            
        Returns:
            Dict[str, Any]: Детальная информация о схеме
        """
        try:
            if self.connector is None:
                return {
                    "success": False,
                    "error": "Нет активного соединения с базой данных"
                }
            
            # Определяем схему для анализа
            target_schema = schema_name or self.connector.get_current_schema()
            
            if not target_schema:
                return {
                    "success": False,
                    "error": "Не удалось определить схему для анализа"
                }
            
            # Получаем список таблиц
            tables = self.connector.get_table_names(target_schema)
            
            # Собираем статистику по таблицам
            table_stats = []
            total_columns = 0
            
            for table in tables:
                try:
                    # Получаем информацию о таблице
                    table_info = self.connector.get_table_info(table, target_schema)
                    columns_info = self.connector.get_column_info(table, target_schema)
                    constraints_info = self.connector.get_constraints_info(table, target_schema)
                    indexes_info = self.connector.get_indexes_info(table, target_schema)
                    
                    column_count = len(columns_info)
                    total_columns += column_count
                    
                    table_stats.append({
                        "table_name": table,
                        "column_count": column_count,
                        "constraint_count": len(constraints_info),
                        "index_count": len(indexes_info),
                        "row_count": table_info.get("num_rows"),
                        "last_analyzed": table_info.get("last_analyzed"),
                        "tablespace": table_info.get("tablespace_name")
                    })
                    
                except Exception as e:
                    logger.warning(f"Ошибка при анализе таблицы {table}: {str(e)}")
                    table_stats.append({
                        "table_name": table,
                        "error": str(e)
                    })
            
            # Подсчитываем общую статистику
            accessible_tables = [t for t in table_stats if "error" not in t]
            total_constraints = sum(t.get("constraint_count", 0) for t in accessible_tables)
            total_indexes = sum(t.get("index_count", 0) for t in accessible_tables)
            total_rows = sum(t.get("row_count", 0) or 0 for t in accessible_tables)
            
            return {
                "success": True,
                "schema_name": target_schema,
                "summary": {
                    "total_tables": len(tables),
                    "accessible_tables": len(accessible_tables),
                    "total_columns": total_columns,
                    "total_constraints": total_constraints,
                    "total_indexes": total_indexes,
                    "total_rows": total_rows
                },
                "tables": table_stats,
                "message": f"Схема {target_schema} содержит {len(tables)} таблиц"
            }
            
        except Exception as e:
            logger.error(f"Ошибка при получении информации о схеме: {str(e)}")
            return {
                "success": False,
                "error": f"Ошибка при получении информации о схеме: {str(e)}"
            }
    
    def list_tables_in_schema(self, schema_name: Optional[str] = None) -> Dict[str, Any]:
        """
        Получает список таблиц в указанной схеме.
        
        Args:
            schema_name: Имя схемы. Если None, используется текущая схема
            
        Returns:
            Dict[str, Any]: Список таблиц в схеме
        """
        try:
            if self.connector is None:
                return {
                    "success": False,
                    "error": "Нет активного соединения с базой данных"
                }
            
            # Определяем схему
            target_schema = schema_name or self.connector.get_current_schema()
            
            if not target_schema:
                return {
                    "success": False,
                    "error": "Не удалось определить схему"
                }
            
            # Получаем список таблиц
            tables = self.connector.get_table_names(target_schema)
            
            if not tables:
                return {
                    "success": True,
                    "schema_name": target_schema,
                    "tables": [],
                    "count": 0,
                    "message": f"В схеме {target_schema} не найдено таблиц"
                }
            
            # Получаем базовую информацию о каждой таблице
            table_list = []
            for table in tables:
                try:
                    table_info = self.connector.get_table_info(table, target_schema)
                    columns_info = self.connector.get_column_info(table, target_schema)
                    
                    table_list.append({
                        "table_name": table,
                        "column_count": len(columns_info),
                        "row_count": table_info.get("num_rows"),
                        "last_analyzed": table_info.get("last_analyzed"),
                        "tablespace": table_info.get("tablespace_name"),
                        "temporary": table_info.get("temporary"),
                        "partitioned": table_info.get("partitioned")
                    })
                    
                except Exception as e:
                    logger.warning(f"Ошибка при получении информации о таблице {table}: {str(e)}")
                    table_list.append({
                        "table_name": table,
                        "error": str(e)
                    })
            
            return {
                "success": True,
                "schema_name": target_schema,
                "tables": table_list,
                "count": len(tables),
                "message": f"В схеме {target_schema} найдено {len(tables)} таблиц"
            }
            
        except Exception as e:
            logger.error(f"Ошибка при получении списка таблиц: {str(e)}")
            return {
                "success": False,
                "error": f"Ошибка при получении списка таблиц: {str(e)}"
            }
    
    def compare_schemas(self, schema1: str, schema2: str) -> Dict[str, Any]:
        """
        Сравнивает две схемы и находит различия.
        
        Args:
            schema1: Имя первой схемы
            schema2: Имя второй схемы
            
        Returns:
            Dict[str, Any]: Результат сравнения схем
        """
        try:
            if self.connector is None:
                return {
                    "success": False,
                    "error": "Нет активного соединения с базой данных"
                }
            
            # Получаем таблицы из обеих схем
            tables1 = set(self.connector.get_table_names(schema1))
            tables2 = set(self.connector.get_table_names(schema2))
            
            # Находим различия
            only_in_schema1 = tables1 - tables2
            only_in_schema2 = tables2 - tables1
            common_tables = tables1 & tables2
            
            return {
                "success": True,
                "schema1": schema1,
                "schema2": schema2,
                "comparison": {
                    "total_tables_schema1": len(tables1),
                    "total_tables_schema2": len(tables2),
                    "common_tables": len(common_tables),
                    "only_in_schema1": len(only_in_schema1),
                    "only_in_schema2": len(only_in_schema2)
                },
                "details": {
                    "common_tables": sorted(list(common_tables)),
                    "only_in_schema1": sorted(list(only_in_schema1)),
                    "only_in_schema2": sorted(list(only_in_schema2))
                },
                "message": f"Сравнение схем {schema1} и {schema2} завершено"
            }
            
        except Exception as e:
            logger.error(f"Ошибка при сравнении схем: {str(e)}")
            return {
                "success": False,
                "error": f"Ошибка при сравнении схем: {str(e)}"
            }
