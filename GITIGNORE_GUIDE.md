# 📋 Git Ignore Guide - Database Explorer MCP

Руководство по использованию .gitignore файла в проекте Database Explorer MCP.

## 🎯 Назначение

Файл `.gitignore` настроен специально для проекта Database Explorer MCP и автоматически исключает из Git:
- Временные файлы
- Конфиденциальные данные
- Сгенерированный контент
- Файлы IDE и операционной системы

## 📁 Структура исключений

### ✅ **Включается в Git (отслеживается)**

#### Основные модули
```
✓ mcp_server.py              # Основной MCP сервер
✓ db_explorer.py             # CLI интерфейс
✓ install_mcp.py             # Установщик
✓ test_mcp_server.py         # Основной тест
✓ config_db.py               # Конфигурация БД
```

#### Директории с кодом
```
✓ mcp_tools/                 # Инструменты MCP
✓ connectors/                # Коннекторы БД
✓ exporters/                 # Экспортеры
✓ models/                    # Модели данных
✓ integrations/              # Интеграции с редакторами
```

#### Конфигурация
```
✓ .env.example               # Пример переменных окружения
✓ requirements.txt           # Зависимости CLI
✓ requirements_mcp.txt       # Зависимости MCP
✓ .gitignore                 # Этот файл
```

#### Документация
```
✓ README.md                  # Основная документация
✓ README_MCP.md              # Документация MCP
✓ QUICKSTART.md              # Быстрый старт
✓ CLEANUP_GUIDE.md           # Руководство по очистке
✓ GITIGNORE_GUIDE.md         # Это руководство
```

#### Batch файлы
```
✓ cleanup_project_en.bat     # Очистка проекта
✓ restore_project.bat        # Восстановление
✓ delete_cleanup.bat         # Удаление
✓ test_cleanup.bat           # Тестирование
```

### ❌ **Исключается из Git (игнорируется)**

#### Python окружение
```
❌ venv/                     # Виртуальное окружение
❌ __pycache__/              # Python кэш
❌ *.pyc, *.pyo, *.pyd       # Скомпилированные файлы
```

#### Конфиденциальные данные
```
❌ .env                      # Реальные переменные окружения
❌ config.ini                # Конфигурация с паролями
❌ credentials.json          # Учетные данные
❌ *.key, *.pem              # Ключи и сертификаты
```

#### Логи и временные файлы
```
❌ logs/                     # Папка логов
❌ *.log                     # Файлы логов
❌ to_delete/                # Папка очистки
❌ *.tmp, *.temp             # Временные файлы
```

#### Сгенерированный контент
```
❌ *_analysis.md             # Анализ таблиц
❌ ffmaprep_analysis.md      # Конкретные анализы
❌ supermag_*.md             # Анализы Supermag
```

#### Тестовые файлы
```
❌ my_test.py                # Личные тесты
❌ test_connection_*.py      # Тесты подключения
❌ query_*.py                # Тестовые запросы
```

#### Старые файлы
```
❌ old_*.md                  # Старые документы
❌ *_prompt.md               # Старые промпты
❌ oracle_connection_prompt.md # Старые конфигурации
```

#### IDE и система
```
❌ .vscode/                  # Visual Studio Code
❌ .idea/                    # PyCharm/IntelliJ
❌ *.swp, *.swo              # Vim
❌ .DS_Store                 # macOS
❌ Thumbs.db                 # Windows
```

## 🔧 Использование

### Проверка статуса
```bash
# Посмотреть какие файлы отслеживаются/игнорируются
git status

# Посмотреть все файлы (включая игнорируемые)
git status --ignored
```

### Добавление файлов
```bash
# Добавить все отслеживаемые файлы
git add .

# Добавить конкретный файл (даже если он в .gitignore)
git add -f filename.txt
```

### Проверка игнорирования
```bash
# Проверить, игнорируется ли файл
git check-ignore filename.txt

# Показать какое правило игнорирует файл
git check-ignore -v filename.txt
```

## 🎯 Специальные правила

### Исключения из игнорирования
```gitignore
# Игнорировать все .txt файлы, кроме requirements
*.txt
!requirements*.txt

# Игнорировать все тестовые файлы, кроме основного
test_*.py
!test_mcp_server.py
```

### Папки с содержимым
```gitignore
# Игнорировать папку полностью
logs/

# Игнорировать содержимое, но не саму папку
logs/*
!logs/.gitkeep
```

## 🚨 Важные замечания

### ⚠️ Безопасность
- **Никогда не коммитьте** файлы с реальными паролями или ключами
- Используйте `.env.example` для примеров конфигурации
- Проверяйте `git status` перед коммитом

### 🔄 Обновление .gitignore
Если вы изменили .gitignore и хотите применить изменения к уже отслеживаемым файлам:

```bash
# Удалить файлы из индекса (но не с диска)
git rm --cached filename.txt

# Или для всех файлов
git rm -r --cached .
git add .
```

### 📋 Проверка перед коммитом
```bash
# 1. Проверить статус
git status

# 2. Убедиться, что нет конфиденциальных файлов
git diff --cached

# 3. Проверить игнорируемые файлы
git status --ignored
```

## 🎉 Результат

После правильной настройки .gitignore ваш репозиторий будет содержать только:
- ✅ Исходный код проекта
- ✅ Документацию
- ✅ Конфигурационные примеры
- ✅ Скрипты и утилиты

И **НЕ будет** содержать:
- ❌ Временные файлы
- ❌ Конфиденциальные данные
- ❌ Сгенерированный контент
- ❌ Файлы IDE

Это обеспечивает чистый, безопасный и профессиональный репозиторий! 🚀

## 📞 Поддержка

Если у вас возникли вопросы по .gitignore:
1. Проверьте `git status --ignored`
2. Используйте `git check-ignore -v filename`
3. Обратитесь к документации Git: https://git-scm.com/docs/gitignore
