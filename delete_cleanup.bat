@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

:: =============================================================================
:: Database Explorer MCP - Удаление папки очистки
:: =============================================================================
:: Этот скрипт окончательно удаляет папку to_delete и все её содержимое
:: ВНИМАНИЕ: Операция необратима!
:: =============================================================================

echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║              Database Explorer MCP - Удаление папки очистки                 ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.

:: Проверка наличия папки to_delete
set "DELETE_DIR=to_delete"
if not exist "%DELETE_DIR%" (
    echo ℹ️  Папка %DELETE_DIR% не найдена - нечего удалять
    echo.
    pause
    exit /b 0
)

echo ✅ Папка %DELETE_DIR% найдена
echo.

:: Подсчет содержимого папки
set /a FILE_COUNT=0
set /a DIR_COUNT=0

for /r "%DELETE_DIR%" %%f in (*.*) do (
    set /a FILE_COUNT+=1
)

for /d /r "%DELETE_DIR%" %%d in (*) do (
    set /a DIR_COUNT+=1
)

echo 📊 Содержимое папки %DELETE_DIR%:
echo    • Файлов: !FILE_COUNT!
echo    • Папок: !DIR_COUNT!
echo.

if !FILE_COUNT! equ 0 if !DIR_COUNT! equ 0 (
    echo ℹ️  Папка %DELETE_DIR% пуста
    rmdir "%DELETE_DIR%" >nul 2>&1
    if !errorlevel! equ 0 (
        echo ✅ Пустая папка %DELETE_DIR% удалена
    ) else (
        echo ❌ Ошибка при удалении пустой папки
    )
    echo.
    pause
    exit /b 0
)

:: Показ содержимого папки
echo 📁 Содержимое папки %DELETE_DIR%:
echo.
dir "%DELETE_DIR%" /b
echo.

:: Множественные предупреждения
echo ⚠️  ⚠️  ⚠️  ВНИМАНИЕ! ⚠️  ⚠️  ⚠️
echo.
echo 🔥 ВЫ СОБИРАЕТЕСЬ ОКОНЧАТЕЛЬНО УДАЛИТЬ ВСЕ СОДЕРЖИМОЕ ПАПКИ %DELETE_DIR%
echo.
echo ❌ ЭТА ОПЕРАЦИЯ НЕОБРАТИМА!
echo ❌ ФАЙЛЫ БУДУТ УДАЛЕНЫ НАВСЕГДА!
echo ❌ ВОССТАНОВЛЕНИЕ БУДЕТ НЕВОЗМОЖНО!
echo.
echo 💡 Альтернативы:
echo    • restore_project.bat - восстановить файлы обратно в проект
echo    • Оставить папку %DELETE_DIR% как резервную копию
echo    • Вручную просмотреть содержимое перед удалением
echo.

:: Первое подтверждение
set /p "CONFIRM1=Вы ДЕЙСТВИТЕЛЬНО хотите удалить папку %DELETE_DIR%? (yes/NO): "

if /i not "!CONFIRM1!"=="yes" (
    echo ✅ Удаление отменено - это правильное решение!
    echo    Папка %DELETE_DIR% сохранена
    echo.
    pause
    exit /b 0
)

echo.
echo ⚠️  ПОСЛЕДНЕЕ ПРЕДУПРЕЖДЕНИЕ!
echo.

:: Второе подтверждение
set /p "CONFIRM2=Введите 'DELETE' для подтверждения окончательного удаления: "

if /i not "!CONFIRM2!"=="DELETE" (
    echo ✅ Удаление отменено
    echo    Папка %DELETE_DIR% сохранена
    echo.
    pause
    exit /b 0
)

echo.
echo 🔥 Начинаем удаление папки %DELETE_DIR%...
echo.

:: Удаление папки со всем содержимым
echo ⏳ Удаление файлов и папок...
rmdir /s /q "%DELETE_DIR%" >nul 2>&1

if !errorlevel! equ 0 (
    echo ✅ Папка %DELETE_DIR% успешно удалена
    echo.
    echo 🎉 Операция завершена!
    echo    Проект полностью очищен от временных файлов
) else (
    echo ❌ Ошибка при удалении папки %DELETE_DIR%
    echo.
    echo 💡 Возможные причины:
    echo    • Файлы используются другими программами
    echo    • Недостаточно прав доступа
    echo    • Слишком длинные пути к файлам
    echo.
    echo 🔧 Попробуйте:
    echo    1. Закрыть все программы, которые могут использовать файлы
    echo    2. Запустить скрипт от имени администратора
    echo    3. Удалить папку вручную через проводник
)

echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                                 ЗАВЕРШЕНО                                   ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.

pause
