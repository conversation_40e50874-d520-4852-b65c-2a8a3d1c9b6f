#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Скрипт для исследования структуры реляционных баз данных и экспорта метаданных в Markdown.
"""

import os
import json
import concurrent.futures
from datetime import datetime
from typing import List, Dict, Any, Optional
from loguru import logger
import argparse

from connectors.factory import DatabaseConnectorFactory
from exporters.markdown import MarkdownExporter
from models.metadata import TableMetadata, SchemaMetadata

# Настройка логирования
os.makedirs("logs", exist_ok=True)
logger.add(
    "logs/db_explorer.log",
    rotation="100 MB",
    retention="30 days",
    format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {function}:{line} | {message}",
    level="INFO",
)


def explore_table(
    connector, table_name: str, schema_name: Optional[str] = None
) -> Dict[str, Any]:
    """
    Исследование структуры таблицы.

    Args:
        connector: Коннектор к базе данных.
        table_name: Имя таблицы.
        schema_name: Имя схемы. Если None, используется текущая схема.

    Returns:
        Dict[str, Any]: Словарь с информацией о таблице.
    """
    logger.info(
        f"Исследование таблицы {schema_name or connector.get_current_schema()}.{table_name}"
    )

    try:
        # Получение информации о таблице
        table_info = connector.get_table_info(table_name, schema_name)

        # Получение информации о столбцах
        columns_info = connector.get_column_info(table_name, schema_name)

        # Получение информации об ограничениях
        constraints_info = connector.get_constraints_info(table_name, schema_name)

        # Получение информации об индексах
        indexes_info = connector.get_indexes_info(table_name, schema_name)

        # Анализ зависимостей
        dependencies_info = connector.get_dependencies_info(table_name, schema_name)

        # Сбор всех метаданных
        metadata = {
            "table_name": table_name,
            "schema_name": schema_name or connector.get_current_schema(),
            "table_info": table_info,
            "columns_info": columns_info,
            "constraints_info": constraints_info,
            "indexes_info": indexes_info,
            "dependencies_info": dependencies_info,
        }

        return metadata

    except Exception as e:
        logger.error(f"Ошибка при исследовании таблицы {table_name}: {str(e)}")
        return {"table_name": table_name, "schema_name": schema_name, "error": str(e)}


def explore_table_wrapper(args):
    """Обертка для функции explore_table для использования в ThreadPoolExecutor."""
    connector, table_name, schema = args
    return explore_table(connector, table_name, schema)


def explore_schema(
    connector, schema_name: Optional[str] = None, max_workers: int = None
) -> List[Dict[str, Any]]:
    """
    Исследование всех таблиц в схеме.

    Args:
        connector: Коннектор к базе данных.
        schema_name: Имя схемы. Если None, используется текущая схема.
        max_workers: Максимальное количество параллельных процессов. Если None, будет использовано
                    количество процессоров * 5 (для I/O-bound задач).

    Returns:
        List[Dict[str, Any]]: Список словарей с информацией о таблицах.
    """
    schema = schema_name or connector.get_current_schema()
    logger.info(f"Исследование схемы {schema}")

    results = []

    try:
        # Получение списка таблиц
        tables = connector.get_table_names(schema)

        if not tables:
            logger.warning(f"В схеме {schema} не найдено таблиц")
            return []

        logger.info(f"Найдено {len(tables)} таблиц в схеме {schema}")

        # Определение количества параллельных процессов
        if max_workers is None:
            import multiprocessing

            max_workers = (
                multiprocessing.cpu_count() * 5
            )  # Для I/O-bound задач можно использовать больше потоков

        logger.info(
            f"Запуск параллельного исследования с использованием {max_workers} потоков"
        )

        # Подготовка аргументов для параллельного выполнения
        args_list = [(connector, table_name, schema) for table_name in tables]

        # Параллельное исследование таблиц
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Запуск задач и получение результатов по мере их завершения
            for table_name, future_result in zip(
                tables, executor.map(explore_table_wrapper, args_list)
            ):
                results.append(future_result)
                logger.info(f"Таблица {schema}.{table_name} успешно исследована")

        return results

    except Exception as e:
        logger.error(f"Ошибка при исследовании схемы {schema}: {str(e)}")
        return results


def export_to_markdown(metadata: Dict[str, Any], output_dir: str = ".") -> str:
    """
    Экспорт метаданных таблицы в Markdown.

    Args:
        metadata: Словарь с метаданными таблицы.
        output_dir: Директория для сохранения файлов Markdown.

    Returns:
        str: Путь к созданному файлу.
    """
    try:
        exporter = MarkdownExporter(output_dir)

        table_name = metadata.get("table_name", "")
        schema_name = metadata.get("schema_name", "")
        table_info = metadata.get("table_info", {})
        columns_info = metadata.get("columns_info", [])
        constraints_info = metadata.get("constraints_info", [])
        indexes_info = metadata.get("indexes_info", [])
        dependencies_info = metadata.get("dependencies_info", [])

        file_path = exporter.export_table_metadata(
            table_name,
            schema_name,
            table_info,
            columns_info,
            constraints_info,
            indexes_info,
            dependencies_info,
        )

        return file_path

    except Exception as e:
        logger.error(f"Ошибка при экспорте метаданных в Markdown: {str(e)}")
        return ""


def export_schema_to_markdown(
    schema_metadata: List[Dict[str, Any]], schema_name: str, output_dir: str = "."
) -> str:
    """
    Экспорт метаданных схемы в Markdown.

    Args:
        schema_metadata: Список словарей с метаданными таблиц.
        schema_name: Имя схемы.
        output_dir: Директория для сохранения файлов Markdown.

    Returns:
        str: Путь к созданному файлу.
    """
    try:
        exporter = MarkdownExporter(output_dir)

        file_path = exporter.export_schema_metadata(schema_name, schema_metadata)

        return file_path

    except Exception as e:
        logger.error(f"Ошибка при экспорте метаданных схемы в Markdown: {str(e)}")
        return ""


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="Исследование структуры реляционных баз данных и экспорт метаданных в Markdown"
    )
    parser.add_argument(
        "--db-type",
        default="oracle",
        help="Тип базы данных (oracle, mysql, postgresql, clickhouse)",
    )
    parser.add_argument("--schema", help="Имя схемы для исследования")
    parser.add_argument("--table", help="Имя таблицы для исследования")
    parser.add_argument(
        "--output-dir", default=".", help="Директория для сохранения файлов Markdown"
    )
    parser.add_argument(
        "--list-schemas", action="store_true", help="Вывести список доступных схем"
    )
    parser.add_argument(
        "--list-tables", action="store_true", help="Вывести список доступных таблиц"
    )
    parser.add_argument(
        "--workers",
        type=int,
        help="Количество параллельных процессов для исследования схемы",
    )

    args = parser.parse_args()

    # Создание коннектора к базе данных
    connector = DatabaseConnectorFactory.create_connector(args.db_type)

    if connector is None:
        logger.error(
            f"Не удалось создать коннектор для типа базы данных: {args.db_type}"
        )
        exit(1)

    # Подключение к базе данных
    if not connector.connect():
        logger.error("Не удалось подключиться к базе данных")
        exit(1)

    try:
        if args.list_schemas:
            schemas = connector.get_schema_names()
            print("Доступные схемы:")
            for schema in schemas:
                print(f"- {schema}")

        elif args.list_tables:
            schema = args.schema or connector.get_current_schema()
            tables = connector.get_table_names(schema)
            print(f"Таблицы в схеме {schema}:")
            for table in tables:
                print(f"- {table}")

        elif args.table:
            # Исследование одной таблицы
            result = explore_table(connector, args.table, args.schema)

            if "error" in result:
                print(
                    f"Ошибка при исследовании таблицы {result['schema_name']}.{result['table_name']}: {result['error']}"
                )
            else:
                # Экспорт метаданных в Markdown
                file_path = export_to_markdown(result, args.output_dir)

                if file_path:
                    print(
                        f"Метаданные таблицы {result['schema_name']}.{result['table_name']} экспортированы в {file_path}"
                    )
                else:
                    print(
                        f"Не удалось экспортировать метаданные таблицы {result['schema_name']}.{result['table_name']}"
                    )

        else:
            # Исследование всей схемы
            schema = args.schema or connector.get_current_schema()
            print(f"Исследование схемы {schema}...")

            # Запуск исследования схемы с параллельной обработкой
            import time

            start_time = time.time()

            results = explore_schema(
                connector, schema_name=schema, max_workers=args.workers
            )

            end_time = time.time()
            elapsed_time = end_time - start_time

            print(f"Исследовано {len(results)} таблиц в схеме {schema}")
            print(f"Время выполнения: {elapsed_time:.2f} секунд")

            # Экспорт метаданных каждой таблицы в отдельный файл Markdown
            for result in results:
                if "error" not in result:
                    file_path = export_to_markdown(result, args.output_dir)
                    if file_path:
                        print(
                            f"Метаданные таблицы {result['schema_name']}.{result['table_name']} экспортированы в {file_path}"
                        )

            # Экспорт общей информации о схеме
            schema_file_path = export_schema_to_markdown(
                results, schema, args.output_dir
            )
            if schema_file_path:
                print(
                    f"Общая информация о схеме {schema} экспортирована в {schema_file_path}"
                )

    finally:
        # Закрытие соединения с базой данных
        connector.disconnect()
