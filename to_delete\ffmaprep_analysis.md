# Анализ таблицы FFMAPREP

## Описание таблицы

Таблица FFMAPREP содержит данные о продажах товаров. Особый интерес представляют записи с RECTYPE = 1, которые содержат информацию о фактических продажах товаров, включая артикул, дату продажи, количество, сумму продажи и другие важные атрибуты транзакции.

## Структура таблицы

| Имя столбца | Тип данных | Длина | Nullable |
|-------------|------------|-------|----------|
| RECTYPE | NUMBER | 22 | N |
| ARTICLE | VARCHAR2 | 50 | N |
| SALETYPE | CHAR | 2 | N |
| SALEID | VARCHAR2 | 50 | N |
| SALESPECITEM | NUMBER | 22 | N |
| SALELOCATIONFROM | NUMBER | 22 | Y |
| SALELOCATIONTO | NUMBER | 22 | Y |
| SALEDATE | DATE | 7 | N |
| SALEOP | NUMBER | 22 | N |
| SALEUSEROP | NUMBER | 22 | Y |
| SALEPAYCASH | CHAR | 1 | N |
| SALECLIENTINDEX | NUMBER | 22 | Y |
| SALEQ | NUMBER | 22 | N |
| SALESUM | NUMBER | 22 | N |
| SALENOVAT | NUMBER | 22 | N |
| SALENOTAX | NUMBER | 22 | N |
| SALECURTYPE | NUMBER | 22 | N |
| SALESUMCUR | NUMBER | 22 | N |
| SALEVATRATE | NUMBER | 22 | N |
| PRIMECOST | NUMBER | 22 | N |
| PRIMECOSTNOVAT | NUMBER | 22 | N |
| PRIMECOSTFORCED | CHAR | 1 | N |
| FORCEDMAPPING | CHAR | 1 | Y |
| QUANTITY | NUMBER | 22 | N |
| INCOMEID | VARCHAR2 | 50 | Y |
| INCOMETYPE | CHAR | 2 | Y |
| INCOMESPECITEM | NUMBER | 22 | Y |
| INCOMECLIENTINDEX | NUMBER | 22 | Y |
| GOODSOWNER | NUMBER | 22 | Y |
| INCOMEQ | NUMBER | 22 | Y |
| INCOMESUM | NUMBER | 22 | Y |
| INCOMENOVAT | NUMBER | 22 | Y |
| INCOMEVATRATE | NUMBER | 22 | Y |
| INCOMECURTYPE | NUMBER | 22 | Y |
| INCOMESUMCUR | NUMBER | 22 | Y |
| INCOMEDATE | DATE | 7 | Y |

## Ограничения таблицы

| Имя ограничения | Тип ограничения | Столбец |
|-----------------|-----------------|--------|
| SYS_C009278 | C | RECTYPE |
| SYS_C009296 | C | QUANTITY |
| SYS_C009280 | C | SALETYPE |
| SYS_C009281 | C | SALEID |
| SYS_C009282 | C | SALESPECITEM |
| SYS_C009283 | C | SALEDATE |
| SYS_C009284 | C | SALEOP |
| SYS_C009285 | C | SALEPAYCASH |
| SYS_C009286 | C | SALEQ |
| SYS_C009287 | C | SALESUM |
| SYS_C009288 | C | SALENOVAT |
| SYS_C009289 | C | SALENOTAX |
| SYS_C009290 | C | SALECURTYPE |
| SYS_C009291 | C | SALESUMCUR |
| SYS_C009292 | C | SALEVATRATE |
| SYS_C009293 | C | PRIMECOST |
| SYS_C009294 | C | PRIMECOSTNOVAT |
| SYS_C009295 | C | PRIMECOSTFORCED |
| SYS_C009279 | C | ARTICLE |

## Связи с другими таблицами

Не удалось получить информацию о связях таблицы или таблица не имеет внешних ключей.

## Статистика по данным с RECTYPE = 1

| Метрика | Значение |
|---------|----------|
| Всего записей | 32253213 |
| Минимальная дата | 2024-01-01 00:00:00 |
| Максимальная дата | 2025-05-25 00:00:00 |
| Общее количество | *********.988 |
| Общая сумма | 15833888553.86 |
| Средняя сумма | 490.924378723446870238943326359454482876 |
| Уникальных артикулов | 49975 |
| Уникальных продаж | 867649 |

## Возможные аналитические отчеты

На основе данных таблицы FFMAPREP с RECTYPE = 1 можно создать следующие аналитические отчеты:

1. **Анализ продаж по периодам** - динамика продаж по дням, неделям, месяцам и годам.
2. **Анализ продаж по товарам** - рейтинг самых продаваемых товаров по количеству и сумме.
3. **Анализ продаж по клиентам** - рейтинг клиентов по объему закупок.
4. **Анализ продаж по операторам** - эффективность работы операторов/продавцов.
5. **Анализ продаж по типам оплаты** - соотношение наличных и безналичных платежей.
6. **Анализ прибыльности продаж** - расчет маржинальности по товарам и категориям.
7. **Анализ возвратов** - статистика по возвратам товаров.
8. **Анализ продаж по локациям** - распределение продаж по торговым точкам.
9. **Прогнозирование продаж** - построение прогнозных моделей на основе исторических данных.
10. **ABC-анализ товаров** - классификация товаров по их вкладу в общий объем продаж.
