#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Установщик Database Explorer MCP для Cursor IDE.
"""

import json
import os
import platform
import shutil
from pathlib import Path


def get_cursor_config_path():
    """Получает путь к конфигурации Cursor."""
    system = platform.system()
    
    if system == "Windows":
        appdata = os.getenv("APPDATA")
        return Path(appdata) / "Cursor" / "User"
    elif system == "Darwin":  # macOS
        home = Path.home()
        return home / "Library" / "Application Support" / "Cursor" / "User"
    elif system == "Linux":
        home = Path.home()
        return home / ".config" / "Cursor" / "User"
    else:
        raise Exception(f"Неподдерживаемая ОС: {system}")


def install_mcp_for_cursor():
    """Устанавливает MCP сервер для Cursor IDE."""
    print("🚀 Установка Database Explorer MCP для Cursor IDE")
    print("=" * 60)
    
    try:
        # Получаем путь к конфигурации
        config_dir = get_cursor_config_path()
        settings_path = config_dir / "settings.json"
        
        print(f"📁 Директория конфигурации: {config_dir}")
        
        # Создаем директорию если не существует
        config_dir.mkdir(parents=True, exist_ok=True)
        
        # Загружаем существующие настройки
        settings = {}
        if settings_path.exists():
            try:
                with open(settings_path, 'r', encoding='utf-8') as f:
                    settings = json.load(f)
                print("✅ Загружены существующие настройки Cursor")
            except json.JSONDecodeError:
                print("⚠️  Файл настроек поврежден, создается новый")
                settings = {}
        else:
            print("📝 Создается новый файл настроек")
        
        # Получаем абсолютный путь к MCP серверу
        current_dir = Path(__file__).parent.parent.parent.absolute()
        mcp_server_path = current_dir / "mcp_server.py"
        
        if not mcp_server_path.exists():
            print(f"❌ MCP сервер не найден: {mcp_server_path}")
            return False
        
        # Настройки MCP
        mcp_config = {
            "mcp.servers": {
                "database-explorer": {
                    "name": "Database Explorer",
                    "description": "MCP сервер для исследования структуры реляционных баз данных",
                    "transport": "stdio",
                    "command": "python",
                    "args": [str(mcp_server_path)],
                    "env": {
                        "DB_TYPE": "oracle"
                    },
                    "capabilities": {
                        "tools": True
                    }
                }
            },
            "mcp.autoStart": True,
            "mcp.logLevel": "info"
        }
        
        # Добавляем переменные окружения если они есть
        env_vars = ["DB_HOST", "DB_PORT", "DB_USER", "DB_PASSWORD", "DB_SERVICE", "DB_NAME"]
        for var in env_vars:
            if os.getenv(var):
                mcp_config["mcp.servers"]["database-explorer"]["env"][var] = os.getenv(var)
        
        # Обновляем настройки
        settings.update(mcp_config)
        
        # Сохраняем настройки
        with open(settings_path, 'w', encoding='utf-8') as f:
            json.dump(settings, f, indent=2, ensure_ascii=False)
        
        print("✅ MCP сервер успешно установлен для Cursor IDE!")
        print(f"📁 Настройки сохранены в: {settings_path}")
        
        # Создаем файл с инструкциями
        instructions_path = config_dir / "database_explorer_mcp_instructions.md"
        with open(instructions_path, 'w', encoding='utf-8') as f:
            f.write("""# Database Explorer MCP для Cursor IDE

## Использование

После перезапуска Cursor IDE вы можете использовать следующие команды:

### Подключение к БД
```
@database-explorer подключись к базе данных Oracle
```

### Исследование схем
```
@database-explorer покажи список всех схем
```

### Анализ таблиц
```
@database-explorer получи структуру таблицы TABLE_NAME
```

### Выполнение запросов
```
@database-explorer выполни запрос: SELECT * FROM DUAL
```

## Доступные инструменты

1. connect_database - Подключение к БД
2. list_schemas - Список схем
3. get_table_structure - Структура таблицы
4. execute_safe_query - Безопасные запросы
5. get_table_sample_data - Образцы данных
... и еще 10 инструментов

## Настройка переменных окружения

Создайте файл .env в корне проекта:
```
DB_TYPE=oracle
DB_HOST=your_host
DB_PORT=1521
DB_USER=your_username
DB_PASSWORD=your_password
DB_SERVICE=your_service_name
```
""")
        
        print(f"📖 Инструкции сохранены в: {instructions_path}")
        print("\n🔄 Перезапустите Cursor IDE для применения изменений")
        
        return True
        
    except Exception as e:
        print(f"❌ Ошибка при установке: {e}")
        return False


if __name__ == "__main__":
    install_mcp_for_cursor()
