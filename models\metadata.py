#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Модели данных для представления метаданных БД.
"""

from dataclasses import dataclass, field
from typing import List, Dict, Any, Optional
from datetime import datetime


@dataclass
class ColumnMetadata:
    """Метаданные столбца таблицы."""
    name: str
    data_type: str
    nullable: bool = True
    data_default: Optional[str] = None
    data_length: Optional[int] = None
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ColumnMetadata':
        """
        Создает объект ColumnMetadata из словаря.
        
        Args:
            data: Словарь с данными.
            
        Returns:
            ColumnMetadata: Объект метаданных столбца.
        """
        return cls(
            name=data.get("column_name", ""),
            data_type=data.get("data_type", ""),
            nullable=data.get("nullable") == "Y",
            data_default=data.get("data_default"),
            data_length=data.get("data_length")
        )


@dataclass
class ConstraintMetadata:
    """Метаданные ограничения таблицы."""
    name: str
    constraint_type: str
    columns: List[str] = field(default_factory=list)
    search_condition: Optional[str] = None
    r_owner: Optional[str] = None
    r_constraint_name: Optional[str] = None
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ConstraintMetadata':
        """
        Создает объект ConstraintMetadata из словаря.
        
        Args:
            data: Словарь с данными.
            
        Returns:
            ConstraintMetadata: Объект метаданных ограничения.
        """
        return cls(
            name=data.get("constraint_name", ""),
            constraint_type=data.get("constraint_type", ""),
            columns=[data.get("column_name")] if data.get("column_name") else [],
            search_condition=data.get("search_condition"),
            r_owner=data.get("r_owner"),
            r_constraint_name=data.get("r_constraint_name")
        )
    
    @classmethod
    def merge_constraints(cls, constraints: List[Dict[str, Any]]) -> List['ConstraintMetadata']:
        """
        Объединяет ограничения с одинаковыми именами.
        
        Args:
            constraints: Список словарей с данными об ограничениях.
            
        Returns:
            List[ConstraintMetadata]: Список объектов метаданных ограничений.
        """
        constraints_by_name = {}
        
        for constraint_data in constraints:
            name = constraint_data.get("constraint_name", "")
            if name not in constraints_by_name:
                constraints_by_name[name] = cls.from_dict(constraint_data)
            else:
                # Добавляем столбец к существующему ограничению
                column_name = constraint_data.get("column_name")
                if column_name and column_name not in constraints_by_name[name].columns:
                    constraints_by_name[name].columns.append(column_name)
        
        return list(constraints_by_name.values())


@dataclass
class IndexMetadata:
    """Метаданные индекса таблицы."""
    name: str
    index_type: str
    uniqueness: str
    columns: List[Dict[str, str]] = field(default_factory=list)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'IndexMetadata':
        """
        Создает объект IndexMetadata из словаря.
        
        Args:
            data: Словарь с данными.
            
        Returns:
            IndexMetadata: Объект метаданных индекса.
        """
        return cls(
            name=data.get("index_name", ""),
            index_type=data.get("index_type", ""),
            uniqueness=data.get("uniqueness", ""),
            columns=[{
                "name": data.get("column_name", ""),
                "descend": data.get("descend", "ASC")
            }] if data.get("column_name") else []
        )
    
    @classmethod
    def merge_indexes(cls, indexes: List[Dict[str, Any]]) -> List['IndexMetadata']:
        """
        Объединяет индексы с одинаковыми именами.
        
        Args:
            indexes: Список словарей с данными об индексах.
            
        Returns:
            List[IndexMetadata]: Список объектов метаданных индексов.
        """
        indexes_by_name = {}
        
        for index_data in indexes:
            name = index_data.get("index_name", "")
            if name not in indexes_by_name:
                indexes_by_name[name] = cls.from_dict(index_data)
            else:
                # Добавляем столбец к существующему индексу
                column_name = index_data.get("column_name")
                descend = index_data.get("descend", "ASC")
                if column_name:
                    # Проверяем, что такого столбца еще нет в индексе
                    if not any(col["name"] == column_name for col in indexes_by_name[name].columns):
                        indexes_by_name[name].columns.append({
                            "name": column_name,
                            "descend": descend
                        })
        
        return list(indexes_by_name.values())


@dataclass
class DependencyMetadata:
    """Метаданные зависимости таблицы."""
    referenced_table: str
    fk_constraint: str
    fk_column: str
    referenced_by_table: str
    referenced_column: str
    dependency_type: str
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'DependencyMetadata':
        """
        Создает объект DependencyMetadata из словаря.
        
        Args:
            data: Словарь с данными.
            
        Returns:
            DependencyMetadata: Объект метаданных зависимости.
        """
        return cls(
            referenced_table=data.get("referenced_table", ""),
            fk_constraint=data.get("fk_constraint", ""),
            fk_column=data.get("fk_column", ""),
            referenced_by_table=data.get("referenced_by_table", ""),
            referenced_column=data.get("referenced_column", ""),
            dependency_type=data.get("dependency_type", "")
        )


@dataclass
class TableMetadata:
    """Метаданные таблицы."""
    name: str
    schema: str
    info: Dict[str, Any] = field(default_factory=dict)
    columns: List[ColumnMetadata] = field(default_factory=list)
    constraints: List[ConstraintMetadata] = field(default_factory=list)
    indexes: List[IndexMetadata] = field(default_factory=list)
    dependencies: List[DependencyMetadata] = field(default_factory=list)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'TableMetadata':
        """
        Создает объект TableMetadata из словаря.
        
        Args:
            data: Словарь с данными.
            
        Returns:
            TableMetadata: Объект метаданных таблицы.
        """
        table_name = data.get("table_name", "")
        schema_name = data.get("schema_name", "")
        
        # Преобразование информации о таблице
        table_info = data.get("table_info", {})
        
        # Преобразование информации о столбцах
        columns_data = data.get("columns_info", [])
        columns = [ColumnMetadata.from_dict(col) for col in columns_data]
        
        # Преобразование информации об ограничениях
        constraints_data = data.get("constraints_info", [])
        constraints = ConstraintMetadata.merge_constraints(constraints_data)
        
        # Преобразование информации об индексах
        indexes_data = data.get("indexes_info", [])
        indexes = IndexMetadata.merge_indexes(indexes_data)
        
        # Преобразование информации о зависимостях
        dependencies_data = data.get("dependencies_info", [])
        dependencies = [DependencyMetadata.from_dict(dep) for dep in dependencies_data]
        
        return cls(
            name=table_name,
            schema=schema_name,
            info=table_info,
            columns=columns,
            constraints=constraints,
            indexes=indexes,
            dependencies=dependencies
        )


@dataclass
class SchemaMetadata:
    """Метаданные схемы."""
    name: str
    tables: List[TableMetadata] = field(default_factory=list)
    analyzed_at: datetime = field(default_factory=datetime.now)
    
    def add_table(self, table: TableMetadata) -> None:
        """
        Добавляет таблицу в схему.
        
        Args:
            table: Объект метаданных таблицы.
        """
        self.tables.append(table)
