#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Скрипт для проверки поддержки MCP в Cursor IDE.
"""

import json
import os
import platform
from pathlib import Path


def get_cursor_config_path():
    """Получает путь к конфигурации Cursor."""
    system = platform.system()
    
    if system == "Windows":
        appdata = os.getenv("APPDATA")
        return Path(appdata) / "Cursor" / "User" / "settings.json"
    elif system == "Darwin":  # macOS
        home = Path.home()
        return home / "Library" / "Application Support" / "Cursor" / "User" / "settings.json"
    elif system == "Linux":
        home = Path.home()
        return home / ".config" / "Cursor" / "User" / "settings.json"
    else:
        raise Exception(f"Неподдерживаемая ОС: {system}")


def check_cursor_mcp_support():
    """Проверяет поддержку MCP в Cursor."""
    print("🔍 Проверка поддержки MCP в Cursor IDE...")
    
    config_path = get_cursor_config_path()
    print(f"📁 Путь к конфигурации: {config_path}")
    
    if not config_path.exists():
        print("❌ Конфигурационный файл Cursor не найден")
        print("💡 Убедитесь, что Cursor IDE установлен и запускался хотя бы раз")
        return False
    
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # Проверяем наличие MCP настроек
        mcp_keys = [key for key in config.keys() if 'mcp' in key.lower()]
        
        if mcp_keys:
            print("✅ Найдены MCP настройки в Cursor:")
            for key in mcp_keys:
                print(f"  - {key}: {config[key]}")
            return True
        else:
            print("⚠️  MCP настройки не найдены в конфигурации Cursor")
            print("💡 Возможно, нужно установить MCP расширение")
            return False
            
    except Exception as e:
        print(f"❌ Ошибка при чтении конфигурации: {e}")
        return False


if __name__ == "__main__":
    check_cursor_mcp_support()
