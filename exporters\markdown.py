#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Экспортер метаданных в формат Markdown.
"""

import os
from typing import Dict, List, Any, Optional
from datetime import datetime
from loguru import logger


class MarkdownExporter:
    """
    Экспортер метаданных в формат Markdown.
    
    Позволяет экспортировать метаданные таблиц в файлы Markdown.
    """
    
    def __init__(self, output_dir: str = "."):
        """
        Инициализация экспортера.
        
        Args:
            output_dir: Директория для сохранения файлов Markdown.
        """
        self.output_dir = output_dir
        
        # Создание директории, если она не существует
        os.makedirs(output_dir, exist_ok=True)
    
    def export_table_metadata(
        self,
        table_name: str,
        schema_name: str,
        table_info: Dict[str, Any],
        columns_info: List[Dict[str, Any]],
        constraints_info: List[Dict[str, Any]],
        indexes_info: List[Dict[str, Any]],
        dependencies_info: List[Dict[str, Any]],
        file_name: Optional[str] = None
    ) -> str:
        """
        Экспортирует метаданные таблицы в файл Markdown.
        
        Args:
            table_name: Имя таблицы.
            schema_name: Имя схемы.
            table_info: Информация о таблице.
            columns_info: Информация о столбцах таблицы.
            constraints_info: Информация об ограничениях таблицы.
            indexes_info: Информация об индексах таблицы.
            dependencies_info: Информация о зависимостях таблицы.
            file_name: Имя файла для сохранения. Если None, используется имя таблицы.
            
        Returns:
            str: Путь к созданному файлу.
        """
        if file_name is None:
            file_name = f"{schema_name}_{table_name}_analysis.md"
        
        file_path = os.path.join(self.output_dir, file_name)
        
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                # Заголовок
                f.write(f"# Анализ таблицы {schema_name}.{table_name}\n\n")
                
                # Дата и время анализа
                f.write(f"*Дата анализа: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*\n\n")
                
                # Основная информация о таблице
                f.write("## Основная информация\n\n")
                
                if table_info:
                    # Форматирование информации о количестве строк
                    num_rows = table_info.get("num_rows")
                    if num_rows is not None:
                        f.write(f"- **Количество строк:** {num_rows:,}\n")
                    
                    # Форматирование даты последнего анализа
                    last_analyzed = table_info.get("last_analyzed")
                    if last_analyzed:
                        if isinstance(last_analyzed, str):
                            f.write(f"- **Последний анализ:** {last_analyzed}\n")
                        else:
                            f.write(f"- **Последний анализ:** {last_analyzed.strftime('%Y-%m-%d %H:%M:%S')}\n")
                    
                    # Добавление информации о табличном пространстве
                    tablespace = table_info.get("tablespace_name")
                    if tablespace:
                        f.write(f"- **Табличное пространство:** {tablespace}\n")
                    
                    # Добавление информации о временной таблице
                    temporary = table_info.get("temporary")
                    if temporary:
                        f.write(f"- **Временная таблица:** {'Да' if temporary == 'Y' else 'Нет'}\n")
                    
                    # Добавление информации о секционировании
                    partitioned = table_info.get("partitioned")
                    if partitioned:
                        f.write(f"- **Секционирована:** {'Да' if partitioned == 'YES' else 'Нет'}\n")
                    
                    # Добавление информации о сжатии
                    compression = table_info.get("compression")
                    compress_for = table_info.get("compress_for")
                    if compression:
                        f.write(f"- **Сжатие:** {compression}")
                        if compress_for:
                            f.write(f" ({compress_for})")
                        f.write("\n")
                    
                    # Добавление других атрибутов
                    for key, value in table_info.items():
                        if key not in ["table_name", "num_rows", "last_analyzed", "tablespace_name",
                                      "temporary", "partitioned", "compression", "compress_for"] and value is not None:
                            f.write(f"- **{key}:** {value}\n")
                
                f.write("\n")
                
                # Информация о столбцах
                if columns_info:
                    f.write("## Столбцы\n\n")
                    f.write("| Имя | Тип данных | Nullable | По умолчанию |\n")
                    f.write("|-----|------------|----------|-------------|\n")
                    
                    for col in columns_info:
                        nullable = "NULL" if col.get("nullable") == "Y" else "NOT NULL"
                        default = col.get("data_default") or ""
                        f.write(f"| {col.get('column_name')} | {col.get('data_type')} | {nullable} | {default} |\n")
                    
                    f.write("\n")
                
                # Информация об ограничениях
                if constraints_info:
                    f.write("## Ограничения\n\n")
                    
                    constraint_types = {
                        "P": "Первичный ключ",
                        "U": "Уникальный ключ",
                        "R": "Внешний ключ",
                        "C": "Проверка"
                    }
                    
                    constraints_by_type = {}
                    for constraint in constraints_info:
                        c_type = constraint.get("constraint_type")
                        if c_type not in constraints_by_type:
                            constraints_by_type[c_type] = []
                        constraints_by_type[c_type].append(constraint)
                    
                    for c_type, constraints in constraints_by_type.items():
                        type_name = constraint_types.get(c_type, f"Тип {c_type}")
                        f.write(f"### {type_name}\n\n")
                        
                        # Группировка по имени ограничения
                        constraints_by_name = {}
                        for constraint in constraints:
                            name = constraint.get("constraint_name")
                            if name not in constraints_by_name:
                                constraints_by_name[name] = []
                            constraints_by_name[name].append(constraint)
                        
                        for name, constraints_group in constraints_by_name.items():
                            columns = ", ".join([c.get("column_name") for c in constraints_group])
                            
                            if c_type == "R":  # Внешний ключ
                                ref_table = constraints_group[0].get("r_owner", "") + "." + constraints_group[0].get("referenced_by_table", "")
                                f.write(f"- **{name}:** ({columns}) -> {ref_table}\n")
                            elif c_type == "C" and constraints_group[0].get("search_condition"):  # Проверка
                                f.write(f"- **{name}:** {constraints_group[0].get('search_condition')}\n")
                            else:
                                f.write(f"- **{name}:** ({columns})\n")
                        
                        f.write("\n")
                
                # Информация об индексах
                if indexes_info:
                    f.write("## Индексы\n\n")
                    f.write("| Имя | Тип | Уникальность | Столбцы |\n")
                    f.write("|-----|-----|--------------|--------|\n")
                    
                    indexes_by_name = {}
                    for idx in indexes_info:
                        name = idx.get("index_name")
                        if name not in indexes_by_name:
                            indexes_by_name[name] = []
                        indexes_by_name[name].append(idx)
                    
                    for name, indexes_group in indexes_by_name.items():
                        uniqueness = indexes_group[0].get("uniqueness", "")
                        idx_type = indexes_group[0].get("index_type", "")
                        columns = ", ".join([f"{idx.get('column_name')} {idx.get('descend')}" for idx in indexes_group])
                        f.write(f"| {name} | {idx_type} | {uniqueness} | {columns} |\n")
                    
                    f.write("\n")
                
                # Информация о зависимостях
                if dependencies_info:
                    f.write("## Зависимости\n\n")
                    
                    # Таблицы, на которые ссылается данная таблица (исходящие зависимости)
                    outgoing_deps = [dep for dep in dependencies_info if dep.get("dependency_type") == "outgoing"]
                    if outgoing_deps:
                        f.write("### Ссылается на\n\n")
                        for dep in outgoing_deps:
                            f.write(f"- **{dep.get('fk_column')}** -> {dep.get('referenced_by_table')}.{dep.get('referenced_column')} (через {dep.get('fk_constraint')})\n")
                        f.write("\n")
                    
                    # Таблицы, которые ссылаются на данную таблицу (входящие зависимости)
                    incoming_deps = [dep for dep in dependencies_info if dep.get("dependency_type") == "incoming"]
                    if incoming_deps:
                        f.write("### На неё ссылаются\n\n")
                        for dep in incoming_deps:
                            f.write(f"- **{dep.get('referenced_table')}.{dep.get('fk_column')}** -> {dep.get('referenced_column')} (через {dep.get('fk_constraint')})\n")
                        f.write("\n")
            
            logger.info(f"Метаданные таблицы {schema_name}.{table_name} успешно экспортированы в {file_path}")
            return file_path
        
        except Exception as e:
            logger.error(f"Ошибка при экспорте метаданных таблицы {schema_name}.{table_name}: {str(e)}")
            return ""
    
    def export_schema_metadata(
        self,
        schema_name: str,
        tables_metadata: List[Dict[str, Any]],
        file_name: Optional[str] = None
    ) -> str:
        """
        Экспортирует метаданные схемы в файл Markdown.
        
        Args:
            schema_name: Имя схемы.
            tables_metadata: Список словарей с метаданными таблиц.
            file_name: Имя файла для сохранения. Если None, используется имя схемы.
            
        Returns:
            str: Путь к созданному файлу.
        """
        if file_name is None:
            file_name = f"{schema_name}_analysis.md"
        
        file_path = os.path.join(self.output_dir, file_name)
        
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                # Заголовок
                f.write(f"# Анализ схемы {schema_name}\n\n")
                
                # Дата и время анализа
                f.write(f"*Дата анализа: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*\n\n")
                
                # Количество таблиц
                f.write(f"## Общая информация\n\n")
                f.write(f"- **Количество таблиц:** {len(tables_metadata)}\n\n")
                
                # Список таблиц
                f.write("## Таблицы\n\n")
                f.write("| Имя таблицы | Количество строк | Количество столбцов | Количество индексов | Количество ограничений |\n")
                f.write("|-------------|-----------------|---------------------|---------------------|------------------------|\n")
                
                for table_metadata in tables_metadata:
                    table_name = table_metadata.get("table_name", "")
                    table_info = table_metadata.get("table_info", {})
                    columns_info = table_metadata.get("columns_info", [])
                    indexes_info = table_metadata.get("indexes_info", [])
                    constraints_info = table_metadata.get("constraints_info", [])
                    
                    num_rows = table_info.get("num_rows", 0) or 0
                    num_columns = len(columns_info)
                    
                    # Подсчет уникальных индексов
                    unique_indexes = set()
                    for idx in indexes_info:
                        unique_indexes.add(idx.get("index_name", ""))
                    num_indexes = len(unique_indexes)
                    
                    # Подсчет уникальных ограничений
                    unique_constraints = set()
                    for constraint in constraints_info:
                        unique_constraints.add(constraint.get("constraint_name", ""))
                    num_constraints = len(unique_constraints)
                    
                    f.write(f"| {table_name} | {num_rows:,} | {num_columns} | {num_indexes} | {num_constraints} |\n")
                
                f.write("\n")
            
            logger.info(f"Метаданные схемы {schema_name} успешно экспортированы в {file_path}")
            return file_path
        
        except Exception as e:
            logger.error(f"Ошибка при экспорте метаданных схемы {schema_name}: {str(e)}")
            return ""
