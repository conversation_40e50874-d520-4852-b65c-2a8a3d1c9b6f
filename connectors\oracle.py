#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Коннектор для работы с Oracle Database.
"""

import os
from typing import List, Dict, Any, Optional, Tuple
from sqlalchemy import text, inspect
from loguru import logger

from connectors.base import DatabaseConnector


class OracleDatabaseConnector(DatabaseConnector):
    """
    Коннектор для работы с Oracle Database.

    Реализует методы базового класса DatabaseConnector для Oracle.
    """

    def __init__(self, connection_params: Optional[Dict[str, Any]] = None):
        """
        Инициализация коннектора.

        Args:
            connection_params: Параметры подключения к базе данных.
                Если None, используются параметры из config_db.py.
        """
        self.connection_params = connection_params
        self.engine = None
        self.session = None
        self.Session = None

    def connect(self) -> bool:
        """
        Устанавливает соединение с базой данных Oracle.

        Returns:
            bool: True, если соединение успешно установлено, иначе False.
        """
        try:
            if self.connection_params:
                # Создание подключения с использованием переданных параметров
                from sqlalchemy import create_engine
                from sqlalchemy.orm import sessionmaker
                import oracledb

                user = self.connection_params.get("user")
                password = self.connection_params.get("password")
                host = self.connection_params.get("host")
                port = self.connection_params.get("port")
                service = self.connection_params.get("service")

                url = f"oracle+oracledb://{user}:{password}@{host}:{port}/{service}"

                self.engine = create_engine(
                    url,
                    pool_size=5,
                    max_overflow=10,
                    pool_timeout=30,
                    pool_recycle=1800,
                    pool_pre_ping=True,
                    echo_pool=True,
                    connect_args={
                        "cclass": "ORACLE_MCP_APP",
                        "purity": oracledb.PURITY_SELF,
                    },
                )

                self.Session = sessionmaker(bind=self.engine)
                self.session = self.Session()
            else:
                # Использование подключения из config_db.py
                from config_db import engine, session, Session

                self.engine = engine
                self.session = session
                self.Session = Session

            # Проверка подключения
            self.session.execute(text("SELECT 1 FROM DUAL"))

            logger.info("Успешное подключение к базе данных Oracle")
            return True
        except Exception as e:
            logger.error(f"Ошибка при подключении к базе данных Oracle: {str(e)}")
            return False

    def disconnect(self) -> None:
        """
        Закрывает соединение с базой данных Oracle.
        """
        if self.session:
            try:
                self.session.close()
                logger.info("Соединение с базой данных Oracle закрыто")
            except Exception as e:
                logger.error(
                    f"Ошибка при закрытии соединения с базой данных Oracle: {str(e)}"
                )

    def execute_query(
        self, query: str, params: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """
        Выполняет SQL-запрос и возвращает результат.

        Args:
            query: SQL-запрос.
            params: Параметры запроса.

        Returns:
            List[Dict[str, Any]]: Результат запроса в виде списка словарей.
        """
        try:
            # Создаем новую сессию для каждого запроса
            with self.Session() as session:
                result = session.execute(text(query), params or {})

                # Преобразование результата в список словарей
                column_names = result.keys()
                rows = []

                for row in result:
                    row_dict = {}
                    for i, column in enumerate(column_names):
                        row_dict[column] = row[i]
                    rows.append(row_dict)

                return rows
        except Exception as e:
            logger.error(f"Ошибка при выполнении запроса: {str(e)}")
            logger.error(f"Запрос: {query}")
            logger.error(f"Параметры: {params}")
            return []

    def get_schema_names(self) -> List[str]:
        """
        Получает список доступных схем в базе данных Oracle.

        Returns:
            List[str]: Список имен схем.
        """
        try:
            inspector = inspect(self.engine)
            schemas = inspector.get_schema_names()
            return schemas
        except Exception as e:
            logger.error(f"Ошибка при получении списка схем: {str(e)}")
            return []

    def get_table_names(self, schema_name: Optional[str] = None) -> List[str]:
        """
        Получает список таблиц в указанной схеме Oracle.

        Args:
            schema_name: Имя схемы. Если None, используется текущая схема.

        Returns:
            List[str]: Список имен таблиц.
        """
        try:
            inspector = inspect(self.engine)
            tables = inspector.get_table_names(schema=schema_name)
            return tables
        except Exception as e:
            logger.error(
                f"Ошибка при получении списка таблиц для схемы {schema_name}: {str(e)}"
            )
            return []

    def get_current_schema(self) -> str:
        """
        Получает имя текущей схемы Oracle.

        Returns:
            str: Имя текущей схемы.
        """
        try:
            with self.Session() as session:
                return session.get_bind().url.username.upper()
        except Exception as e:
            logger.error(f"Ошибка при получении текущей схемы: {str(e)}")
            return ""

    def get_table_info(
        self, table_name: str, schema_name: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Получает информацию о таблице Oracle.

        Args:
            table_name: Имя таблицы.
            schema_name: Имя схемы. Если None, используется текущая схема.

        Returns:
            Dict[str, Any]: Информация о таблице.
        """
        try:
            query = """
            SELECT table_name, num_rows, last_analyzed,
                   tablespace_name, temporary, partitioned,
                   compression, compress_for
            FROM all_tables
            WHERE owner = :schema
            AND table_name = :table
            """

            schema = schema_name or self.get_current_schema()
            result = self.execute_query(
                query, {"schema": schema, "table": table_name.upper()}
            )

            if result:
                return result[0]
            else:
                # Альтернативный подход через SQLAlchemy
                inspector = inspect(self.engine)
                if inspector.has_table(table_name, schema=schema_name):
                    return {
                        "table_name": table_name,
                        "num_rows": None,
                        "last_analyzed": None,
                        "tablespace_name": None,
                        "temporary": None,
                        "partitioned": None,
                        "compression": None,
                        "compress_for": None,
                    }
                else:
                    return {}
        except Exception as e:
            logger.error(
                f"Ошибка при получении информации о таблице {table_name}: {str(e)}"
            )
            return {}

    def get_column_info(
        self, table_name: str, schema_name: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        Получает информацию о столбцах таблицы Oracle.

        Args:
            table_name: Имя таблицы.
            schema_name: Имя схемы. Если None, используется текущая схема.

        Returns:
            List[Dict[str, Any]]: Список словарей с информацией о столбцах.
        """
        try:
            query = """
            SELECT column_name, data_type, data_length, nullable, data_default
            FROM all_tab_columns
            WHERE table_name = :table
            AND owner = :schema
            ORDER BY column_id
            """

            schema = schema_name or self.get_current_schema()
            result = self.execute_query(
                query, {"table": table_name.upper(), "schema": schema}
            )

            if not result:
                # Альтернативный подход через SQLAlchemy
                inspector = inspect(self.engine)
                columns = inspector.get_columns(table_name, schema=schema_name)
                return [
                    {
                        "column_name": col["name"],
                        "data_type": str(col["type"]),
                        "nullable": "Y" if col.get("nullable", True) else "N",
                        "data_default": None,
                        "data_length": None,
                    }
                    for col in columns
                ]

            return result
        except Exception as e:
            logger.error(
                f"Ошибка при получении информации о столбцах таблицы {table_name}: {str(e)}"
            )
            return []

    def get_constraints_info(
        self, table_name: str, schema_name: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        Получает информацию об ограничениях таблицы Oracle.

        Args:
            table_name: Имя таблицы.
            schema_name: Имя схемы. Если None, используется текущая схема.

        Returns:
            List[Dict[str, Any]]: Список словарей с информацией об ограничениях.
        """
        try:
            query = """
            SELECT c.constraint_name, c.constraint_type, c.search_condition,
                   c.r_owner, c.r_constraint_name,
                   cc.column_name
            FROM all_constraints c
            JOIN all_cons_columns cc ON c.constraint_name = cc.constraint_name
            WHERE c.table_name = :table
            AND c.owner = :schema
            ORDER BY c.constraint_type, cc.position
            """

            schema = schema_name or self.get_current_schema()
            result = self.execute_query(
                query, {"table": table_name.upper(), "schema": schema}
            )

            if not result:
                # Альтернативный подход через SQLAlchemy
                inspector = inspect(self.engine)
                constraints = []

                # Получение первичных ключей
                pks = inspector.get_pk_constraint(table_name, schema=schema_name)
                if pks and pks.get("constrained_columns"):
                    for col in pks.get("constrained_columns", []):
                        constraints.append(
                            {
                                "constraint_name": pks.get("name", "PK"),
                                "constraint_type": "P",
                                "search_condition": None,
                                "r_owner": None,
                                "r_constraint_name": None,
                                "column_name": col,
                            }
                        )

                # Получение внешних ключей
                fks = inspector.get_foreign_keys(table_name, schema=schema_name)
                for fk in fks:
                    for col in fk.get("constrained_columns", []):
                        constraints.append(
                            {
                                "constraint_name": fk.get("name", "FK"),
                                "constraint_type": "R",
                                "search_condition": None,
                                "r_owner": fk.get("referred_schema"),
                                "r_constraint_name": None,
                                "column_name": col,
                            }
                        )

                # Получение уникальных ограничений
                uks = inspector.get_unique_constraints(table_name, schema=schema_name)
                for uk in uks:
                    for col in uk.get("column_names", []):
                        constraints.append(
                            {
                                "constraint_name": uk.get("name", "UK"),
                                "constraint_type": "U",
                                "search_condition": None,
                                "r_owner": None,
                                "r_constraint_name": None,
                                "column_name": col,
                            }
                        )

                return constraints

            return result
        except Exception as e:
            logger.error(
                f"Ошибка при получении информации об ограничениях таблицы {table_name}: {str(e)}"
            )
            return []

    def get_indexes_info(
        self, table_name: str, schema_name: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        Получает информацию об индексах таблицы Oracle.

        Args:
            table_name: Имя таблицы.
            schema_name: Имя схемы. Если None, используется текущая схема.

        Returns:
            List[Dict[str, Any]]: Список словарей с информацией об индексах.
        """
        try:
            query = """
            SELECT i.index_name, i.index_type, i.uniqueness,
                   ic.column_name, ic.descend
            FROM all_indexes i
            JOIN all_ind_columns ic ON i.index_name = ic.index_name
            WHERE i.table_name = :table
            AND i.owner = :schema
            ORDER BY i.index_name, ic.column_position
            """

            schema = schema_name or self.get_current_schema()
            result = self.execute_query(
                query, {"table": table_name.upper(), "schema": schema}
            )

            if not result:
                # Альтернативный подход через SQLAlchemy
                inspector = inspect(self.engine)
                indexes = []

                for idx in inspector.get_indexes(table_name, schema=schema_name):
                    for col in idx.get("column_names", []):
                        indexes.append(
                            {
                                "index_name": idx.get("name", ""),
                                "index_type": "NORMAL",
                                "uniqueness": (
                                    "UNIQUE"
                                    if idx.get("unique", False)
                                    else "NONUNIQUE"
                                ),
                                "column_name": col,
                                "descend": "ASC",
                            }
                        )

                return indexes

            return result
        except Exception as e:
            logger.error(
                f"Ошибка при получении информации об индексах таблицы {table_name}: {str(e)}"
            )
            return []

    def get_dependencies_info(
        self, table_name: str, schema_name: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        Получает информацию о зависимостях таблицы Oracle (внешние ключи).

        Args:
            table_name: Имя таблицы.
            schema_name: Имя схемы. Если None, используется текущая схема.

        Returns:
            List[Dict[str, Any]]: Список словарей с информацией о зависимостях.
        """
        try:
            # Оптимизированный запрос для получения зависимостей
            query = """
            -- Получение внешних ключей, где таблица ссылается на другие
            SELECT a.table_name as referenced_table,
                   a.constraint_name as fk_constraint,
                   acc.column_name as fk_column,
                   r.table_name as referenced_by_table,
                   rc.column_name as referenced_column,
                   'outgoing' as dependency_type
            FROM all_constraints a
            JOIN all_cons_columns acc ON a.constraint_name = acc.constraint_name AND a.owner = acc.owner
            JOIN all_constraints r ON a.r_constraint_name = r.constraint_name
            JOIN all_cons_columns rc ON r.constraint_name = rc.constraint_name AND r.owner = rc.owner
            WHERE a.constraint_type = 'R'
            AND a.table_name = :table
            AND a.owner = :schema

            UNION ALL

            -- Получение внешних ключей, где на таблицу ссылаются другие
            SELECT a.table_name as referenced_table,
                   a.constraint_name as fk_constraint,
                   acc.column_name as fk_column,
                   r.table_name as referenced_by_table,
                   rc.column_name as referenced_column,
                   'incoming' as dependency_type
            FROM all_constraints a
            JOIN all_cons_columns acc ON a.constraint_name = acc.constraint_name AND a.owner = acc.owner
            JOIN all_constraints r ON a.r_constraint_name = r.constraint_name
            JOIN all_cons_columns rc ON r.constraint_name = rc.constraint_name AND r.owner = rc.owner
            WHERE a.constraint_type = 'R'
            AND r.table_name = :table
            AND r.owner = :schema

            ORDER BY dependency_type, referenced_table
            """

            schema = schema_name or self.get_current_schema()
            result = self.execute_query(
                query, {"table": table_name.upper(), "schema": schema}
            )

            if not result:
                # Альтернативный подход через SQLAlchemy - только для текущей таблицы
                inspector = inspect(self.engine)
                dependencies = []

                # Получение внешних ключей, где таблица ссылается на другие
                fks = inspector.get_foreign_keys(table_name, schema=schema_name)
                for fk in fks:
                    for i, col in enumerate(fk.get("constrained_columns", [])):
                        ref_col = (
                            fk.get("referred_columns", [])[i]
                            if i < len(fk.get("referred_columns", []))
                            else None
                        )
                        dependencies.append(
                            {
                                "referenced_table": table_name,
                                "fk_constraint": fk.get("name", "FK"),
                                "fk_column": col,
                                "referenced_by_table": fk.get("referred_table"),
                                "referenced_column": ref_col,
                                "dependency_type": "outgoing",
                            }
                        )

                return dependencies

            return result
        except Exception as e:
            logger.error(
                f"Ошибка при анализе зависимостей таблицы {table_name}: {str(e)}"
            )
            return []
