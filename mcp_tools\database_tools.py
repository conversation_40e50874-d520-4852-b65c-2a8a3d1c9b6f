#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
MCP инструменты для работы с подключением к базе данных.
"""

import os
import re
from typing import Dict, Any, Optional, List
from loguru import logger

from connectors.factory import DatabaseConnectorFactory


class DatabaseTools:
    """
    Класс для инструментов работы с подключением к базе данных.
    """
    
    def __init__(self):
        self.connector = None
        self.connection_params = None
        self.db_type = None
    
    def connect_database(
        self, 
        db_type: str, 
        host: Optional[str] = None,
        port: Optional[int] = None,
        user: Optional[str] = None,
        password: Optional[str] = None,
        service: Optional[str] = None,
        database: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Подключается к базе данных с указанными параметрами.
        
        Args:
            db_type: Тип базы данных (oracle, mysql, postgresql, clickhouse)
            host: Хост базы данных
            port: Порт базы данных
            user: Имя пользователя
            password: Пароль
            service: Имя сервиса (для Oracle)
            database: Имя базы данных (для MySQL, PostgreSQL)
            
        Returns:
            Dict[str, Any]: Результат подключения
        """
        try:
            # Проверка типа базы данных
            supported_db_types = ['oracle', 'mysql', 'postgresql', 'clickhouse']
            if db_type.lower() not in supported_db_types:
                return {
                    "success": False,
                    "error": f"Неподдерживаемый тип базы данных: {db_type}. Поддерживаются: {', '.join(supported_db_types)}"
                }
            
            # Если параметры не переданы, используем переменные окружения
            connection_params = {}
            
            if host:
                connection_params["host"] = host
            elif os.getenv("DB_HOST"):
                connection_params["host"] = os.getenv("DB_HOST")
            else:
                return {"success": False, "error": "Не указан хост базы данных"}
            
            if port:
                connection_params["port"] = port
            elif os.getenv("DB_PORT"):
                connection_params["port"] = int(os.getenv("DB_PORT"))
            else:
                # Устанавливаем порты по умолчанию
                default_ports = {
                    "oracle": 1521,
                    "mysql": 3306,
                    "postgresql": 5432,
                    "clickhouse": 9000
                }
                connection_params["port"] = default_ports.get(db_type.lower(), 1521)
            
            if user:
                connection_params["user"] = user
            elif os.getenv("DB_USER"):
                connection_params["user"] = os.getenv("DB_USER")
            else:
                return {"success": False, "error": "Не указано имя пользователя"}
            
            if password:
                connection_params["password"] = password
            elif os.getenv("DB_PASSWORD"):
                connection_params["password"] = os.getenv("DB_PASSWORD")
            else:
                return {"success": False, "error": "Не указан пароль"}
            
            # Специфичные параметры для разных СУБД
            if db_type.lower() == "oracle":
                if service:
                    connection_params["service"] = service
                elif os.getenv("DB_SERVICE"):
                    connection_params["service"] = os.getenv("DB_SERVICE")
                else:
                    return {"success": False, "error": "Не указано имя сервиса Oracle"}
            
            elif db_type.lower() in ["mysql", "postgresql"]:
                if database:
                    connection_params["database"] = database
                elif os.getenv("DB_NAME"):
                    connection_params["database"] = os.getenv("DB_NAME")
                else:
                    return {"success": False, "error": "Не указано имя базы данных"}
            
            # Создание коннектора
            self.connector = DatabaseConnectorFactory.create_connector(db_type, connection_params)
            
            if self.connector is None:
                return {
                    "success": False,
                    "error": f"Не удалось создать коннектор для типа базы данных: {db_type}"
                }
            
            # Попытка подключения
            if self.connector.connect():
                self.connection_params = connection_params
                self.db_type = db_type
                
                # Получаем информацию о подключении
                current_schema = self.connector.get_current_schema()
                
                return {
                    "success": True,
                    "message": f"Успешное подключение к базе данных {db_type}",
                    "db_type": db_type,
                    "host": connection_params["host"],
                    "port": connection_params["port"],
                    "user": connection_params["user"],
                    "current_schema": current_schema
                }
            else:
                return {
                    "success": False,
                    "error": "Не удалось установить соединение с базой данных"
                }
                
        except Exception as e:
            logger.error(f"Ошибка при подключении к базе данных: {str(e)}")
            return {
                "success": False,
                "error": f"Ошибка при подключении: {str(e)}"
            }
    
    def disconnect_database(self) -> Dict[str, Any]:
        """
        Отключается от базы данных.
        
        Returns:
            Dict[str, Any]: Результат отключения
        """
        try:
            if self.connector:
                self.connector.disconnect()
                self.connector = None
                self.connection_params = None
                self.db_type = None
                
                return {
                    "success": True,
                    "message": "Соединение с базой данных закрыто"
                }
            else:
                return {
                    "success": False,
                    "error": "Нет активного соединения с базой данных"
                }
                
        except Exception as e:
            logger.error(f"Ошибка при отключении от базы данных: {str(e)}")
            return {
                "success": False,
                "error": f"Ошибка при отключении: {str(e)}"
            }
    
    def get_connection_status(self) -> Dict[str, Any]:
        """
        Получает статус подключения к базе данных.
        
        Returns:
            Dict[str, Any]: Статус подключения
        """
        try:
            if self.connector is None:
                return {
                    "connected": False,
                    "message": "Нет активного соединения с базой данных"
                }
            
            # Проверяем соединение простым запросом
            if self.db_type == "oracle":
                test_query = "SELECT 1 FROM DUAL"
            else:
                test_query = "SELECT 1"
            
            result = self.connector.execute_query(test_query)
            
            if result:
                current_schema = self.connector.get_current_schema()
                return {
                    "connected": True,
                    "db_type": self.db_type,
                    "host": self.connection_params.get("host"),
                    "port": self.connection_params.get("port"),
                    "user": self.connection_params.get("user"),
                    "current_schema": current_schema,
                    "message": "Соединение активно"
                }
            else:
                return {
                    "connected": False,
                    "message": "Соединение неактивно"
                }
                
        except Exception as e:
            logger.error(f"Ошибка при проверке статуса соединения: {str(e)}")
            return {
                "connected": False,
                "error": f"Ошибка при проверке соединения: {str(e)}"
            }
    
    def execute_safe_query(self, query: str, limit: int = 100) -> Dict[str, Any]:
        """
        Выполняет безопасный SELECT запрос с ограничениями.
        
        Args:
            query: SQL запрос (только SELECT)
            limit: Максимальное количество возвращаемых строк
            
        Returns:
            Dict[str, Any]: Результат выполнения запроса
        """
        try:
            if self.connector is None:
                return {
                    "success": False,
                    "error": "Нет активного соединения с базой данных"
                }
            
            # Проверка безопасности запроса
            query_clean = query.strip().upper()
            
            # Разрешены только SELECT запросы
            if not query_clean.startswith("SELECT"):
                return {
                    "success": False,
                    "error": "Разрешены только SELECT запросы"
                }
            
            # Запрещенные ключевые слова
            forbidden_keywords = [
                "INSERT", "UPDATE", "DELETE", "DROP", "CREATE", "ALTER", 
                "TRUNCATE", "GRANT", "REVOKE", "EXEC", "EXECUTE"
            ]
            
            for keyword in forbidden_keywords:
                if keyword in query_clean:
                    return {
                        "success": False,
                        "error": f"Запрещенное ключевое слово: {keyword}"
                    }
            
            # Ограничение количества строк
            if limit > 1000:
                limit = 1000
            
            # Добавляем LIMIT/ROWNUM в зависимости от СУБД
            if self.db_type == "oracle":
                if "ROWNUM" not in query_clean and "FETCH FIRST" not in query_clean:
                    query = f"SELECT * FROM ({query}) WHERE ROWNUM <= {limit}"
            elif self.db_type in ["mysql", "postgresql"]:
                if "LIMIT" not in query_clean:
                    query = f"{query} LIMIT {limit}"
            
            # Выполнение запроса
            result = self.connector.execute_query(query)
            
            return {
                "success": True,
                "data": result,
                "row_count": len(result),
                "limit_applied": limit,
                "message": f"Запрос выполнен успешно. Возвращено {len(result)} строк"
            }
            
        except Exception as e:
            logger.error(f"Ошибка при выполнении запроса: {str(e)}")
            return {
                "success": False,
                "error": f"Ошибка при выполнении запроса: {str(e)}"
            }
