#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Скрипт для тестирования пула соединений SQLAlchemy к базе данных Oracle.
"""

import os
import sys
import time
import threading
import concurrent.futures
from loguru import logger
from sqlalchemy import text

# Настройка логирования
logger.add(
    "logs/connection_pool_test.log",
    rotation="100 MB",
    retention="30 days",
    format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {function}:{line} | {message}",
    level="INFO"
)

# Создание директории для логов, если она не существует
os.makedirs("logs", exist_ok=True)

def execute_query(query_id):
    """Выполнение запроса к базе данных."""
    try:
        # Импорт существующей конфигурации
        from config_db import session, engine

        start_time = time.time()

        # Выполнение простого запроса
        result = session.execute(text("SELECT 'Запрос выполнен успешно!' FROM DUAL"))
        row = result.fetchone()

        # Получение информации о соединении
        connection_info = session.execute(text("""
            SELECT
                sys_context('USERENV', 'SESSION_USER') as username,
                sys_context('USERENV', 'SID') as session_id,
                sys_context('USERENV', 'HOST') as hostname
            FROM DUAL
        """)).fetchone()

        end_time = time.time()
        execution_time = end_time - start_time

        logger.info(f"Запрос {query_id}: Выполнен за {execution_time:.4f} сек. "
                   f"Сессия: {connection_info.session_id}, "
                   f"Пользователь: {connection_info.username}")

        return {
            "query_id": query_id,
            "execution_time": execution_time,
            "session_id": connection_info.session_id,
            "username": connection_info.username,
            "hostname": connection_info.hostname,
            "result": row[0]
        }
    except Exception as e:
        logger.error(f"Ошибка при выполнении запроса {query_id}: {str(e)}")
        return {
            "query_id": query_id,
            "error": str(e)
        }

def test_sequential_queries(num_queries=10):
    """Тестирование последовательных запросов."""
    logger.info(f"Начало тестирования {num_queries} последовательных запросов...")

    start_time = time.time()
    results = []

    for i in range(num_queries):
        result = execute_query(f"SEQ-{i+1}")
        results.append(result)

    end_time = time.time()
    total_time = end_time - start_time

    logger.info(f"Завершено тестирование последовательных запросов. "
               f"Общее время: {total_time:.4f} сек. "
               f"Среднее время на запрос: {total_time/num_queries:.4f} сек.")

    return results, total_time

def test_concurrent_queries(num_queries=10, max_workers=5):
    """Тестирование параллельных запросов с использованием пула потоков."""
    logger.info(f"Начало тестирования {num_queries} параллельных запросов "
               f"с {max_workers} рабочими потоками...")

    start_time = time.time()
    results = []

    with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
        # Создание списка задач
        futures = [executor.submit(execute_query, f"CONC-{i+1}") for i in range(num_queries)]

        # Получение результатов по мере их завершения
        for future in concurrent.futures.as_completed(futures):
            results.append(future.result())

    end_time = time.time()
    total_time = end_time - start_time

    logger.info(f"Завершено тестирование параллельных запросов. "
               f"Общее время: {total_time:.4f} сек. "
               f"Среднее время на запрос: {total_time/num_queries:.4f} сек.")

    return results, total_time

def print_pool_stats():
    """Вывод статистики пула соединений."""
    from config_db import engine

    pool = engine.pool

    stats = {
        "Размер пула": pool.size(),
        "Количество занятых соединений": pool.checkedin(),
        "Количество свободных соединений": pool.checkedout(),
        "Максимальный размер пула": pool.size() + pool._max_overflow,
        "Время жизни соединения": f"{pool._recycle} секунд"
    }

    print("\nСтатистика пула соединений:")
    for key, value in stats.items():
        print(f"  {key}: {value}")

    return stats

def analyze_results(sequential_results, concurrent_results):
    """Анализ результатов тестирования."""
    seq_data, seq_total_time = sequential_results
    conc_data, conc_total_time = concurrent_results

    num_seq_queries = len(seq_data)
    num_conc_queries = len(conc_data)

    # Расчет средних значений
    seq_avg_time = seq_total_time / num_seq_queries
    conc_avg_time = conc_total_time / num_conc_queries

    # Расчет ускорения
    speedup = seq_total_time / conc_total_time

    # Сбор уникальных сессий
    seq_sessions = set(item.get("session_id") for item in seq_data if "session_id" in item)
    conc_sessions = set(item.get("session_id") for item in conc_data if "session_id" in item)

    print("\nАнализ результатов тестирования:")
    print(f"  Последовательные запросы ({num_seq_queries}):")
    print(f"    Общее время: {seq_total_time:.4f} сек.")
    print(f"    Среднее время на запрос: {seq_avg_time:.4f} сек.")
    print(f"    Количество уникальных сессий: {len(seq_sessions)}")

    print(f"  Параллельные запросы ({num_conc_queries}):")
    print(f"    Общее время: {conc_total_time:.4f} сек.")
    print(f"    Среднее время на запрос: {conc_avg_time:.4f} сек.")
    print(f"    Количество уникальных сессий: {len(conc_sessions)}")

    print(f"  Ускорение при использовании пула соединений: {speedup:.2f}x")

    return {
        "sequential": {
            "total_time": seq_total_time,
            "avg_time": seq_avg_time,
            "unique_sessions": len(seq_sessions)
        },
        "concurrent": {
            "total_time": conc_total_time,
            "avg_time": conc_avg_time,
            "unique_sessions": len(conc_sessions)
        },
        "speedup": speedup
    }

if __name__ == "__main__":
    print("Тестирование пула соединений SQLAlchemy к базе данных Oracle...")

    # Количество запросов для тестирования
    num_queries = 20

    # Вывод начальной статистики пула
    print_pool_stats()

    # Тестирование последовательных запросов
    print(f"\nВыполнение {num_queries} последовательных запросов...")
    sequential_results = test_sequential_queries(num_queries)

    # Вывод статистики пула после последовательных запросов
    print_pool_stats()

    # Тестирование параллельных запросов
    print(f"\nВыполнение {num_queries} параллельных запросов...")
    concurrent_results = test_concurrent_queries(num_queries)

    # Вывод статистики пула после параллельных запросов
    print_pool_stats()

    # Анализ результатов
    analysis = analyze_results(sequential_results, concurrent_results)

    print("\nТестирование пула соединений завершено!")
    print(f"Подробные логи доступны в файле: logs/connection_pool_test.log")
