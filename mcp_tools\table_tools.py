#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
MCP инструменты для работы с таблицами базы данных.
"""

from typing import Dict, Any, Optional, List
from loguru import logger


class TableTools:
    """
    Класс для инструментов работы с таблицами базы данных.
    """

    def __init__(self, database_tools):
        """
        Инициализация с ссылкой на DatabaseTools.

        Args:
            database_tools: Экземпляр DatabaseTools для доступа к коннектору
        """
        self.database_tools = database_tools

    @property
    def connector(self):
        """Получает коннектор из database_tools."""
        return self.database_tools.connector

    def get_table_structure(
        self, table_name: str, schema_name: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Получает полную структуру таблицы.

        Args:
            table_name: Имя таблицы
            schema_name: Имя схемы. Если None, используется текущая схема

        Returns:
            Dict[str, Any]: Полная структура таблицы
        """
        try:
            if self.connector is None:
                return {
                    "success": False,
                    "error": "Нет активного соединения с базой данных",
                }

            # Определяем схему
            target_schema = schema_name or self.connector.get_current_schema()

            # Получаем всю информацию о таблице
            table_info = self.connector.get_table_info(table_name, target_schema)
            columns_info = self.connector.get_column_info(table_name, target_schema)
            constraints_info = self.connector.get_constraints_info(
                table_name, target_schema
            )
            indexes_info = self.connector.get_indexes_info(table_name, target_schema)
            dependencies_info = self.connector.get_dependencies_info(
                table_name, target_schema
            )

            # Группируем ограничения по типам
            constraints_by_type = {}
            for constraint in constraints_info:
                constraint_type = constraint.get("constraint_type", "UNKNOWN")
                if constraint_type not in constraints_by_type:
                    constraints_by_type[constraint_type] = []
                constraints_by_type[constraint_type].append(constraint)

            # Группируем индексы по типам
            indexes_by_type = {}
            for index in indexes_info:
                index_type = index.get("index_type", "UNKNOWN")
                if index_type not in indexes_by_type:
                    indexes_by_type[index_type] = []
                indexes_by_type[index_type].append(index)

            return {
                "success": True,
                "table_name": table_name,
                "schema_name": target_schema,
                "table_info": table_info,
                "columns": {"count": len(columns_info), "details": columns_info},
                "constraints": {
                    "count": len(constraints_info),
                    "by_type": constraints_by_type,
                    "details": constraints_info,
                },
                "indexes": {
                    "count": len(indexes_info),
                    "by_type": indexes_by_type,
                    "details": indexes_info,
                },
                "dependencies": {
                    "count": len(dependencies_info),
                    "details": dependencies_info,
                },
                "message": f"Структура таблицы {target_schema}.{table_name} получена успешно",
            }

        except Exception as e:
            logger.error(f"Ошибка при получении структуры таблицы: {str(e)}")
            return {
                "success": False,
                "error": f"Ошибка при получении структуры таблицы: {str(e)}",
            }

    def get_table_columns(
        self, table_name: str, schema_name: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Получает информацию о столбцах таблицы.

        Args:
            table_name: Имя таблицы
            schema_name: Имя схемы. Если None, используется текущая схема

        Returns:
            Dict[str, Any]: Информация о столбцах таблицы
        """
        try:
            if self.connector is None:
                return {
                    "success": False,
                    "error": "Нет активного соединения с базой данных",
                }

            # Определяем схему
            target_schema = schema_name or self.connector.get_current_schema()

            # Получаем информацию о столбцах
            columns_info = self.connector.get_column_info(table_name, target_schema)

            if not columns_info:
                return {
                    "success": False,
                    "error": f"Таблица {target_schema}.{table_name} не найдена или нет доступа",
                }

            # Анализируем типы данных
            data_types = {}
            nullable_count = 0
            primary_key_columns = []

            for column in columns_info:
                data_type = column.get("data_type", "UNKNOWN")
                if data_type not in data_types:
                    data_types[data_type] = 0
                data_types[data_type] += 1

                if column.get("nullable") == "Y":
                    nullable_count += 1

                if column.get("primary_key"):
                    primary_key_columns.append(column.get("column_name"))

            return {
                "success": True,
                "table_name": table_name,
                "schema_name": target_schema,
                "columns": {
                    "count": len(columns_info),
                    "nullable_count": nullable_count,
                    "not_null_count": len(columns_info) - nullable_count,
                    "primary_key_columns": primary_key_columns,
                    "data_types": data_types,
                    "details": columns_info,
                },
                "message": f"Информация о {len(columns_info)} столбцах таблицы {target_schema}.{table_name}",
            }

        except Exception as e:
            logger.error(f"Ошибка при получении информации о столбцах: {str(e)}")
            return {
                "success": False,
                "error": f"Ошибка при получении информации о столбцах: {str(e)}",
            }

    def get_table_constraints(
        self, table_name: str, schema_name: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Получает информацию об ограничениях таблицы.

        Args:
            table_name: Имя таблицы
            schema_name: Имя схемы. Если None, используется текущая схема

        Returns:
            Dict[str, Any]: Информация об ограничениях таблицы
        """
        try:
            if self.connector is None:
                return {
                    "success": False,
                    "error": "Нет активного соединения с базой данных",
                }

            # Определяем схему
            target_schema = schema_name or self.connector.get_current_schema()

            # Получаем информацию об ограничениях
            constraints_info = self.connector.get_constraints_info(
                table_name, target_schema
            )

            # Группируем ограничения по типам
            constraints_by_type = {}
            for constraint in constraints_info:
                constraint_type = constraint.get("constraint_type", "UNKNOWN")
                if constraint_type not in constraints_by_type:
                    constraints_by_type[constraint_type] = []
                constraints_by_type[constraint_type].append(constraint)

            # Подсчитываем статистику
            constraint_counts = {
                ctype: len(constraints)
                for ctype, constraints in constraints_by_type.items()
            }

            return {
                "success": True,
                "table_name": table_name,
                "schema_name": target_schema,
                "constraints": {
                    "total_count": len(constraints_info),
                    "by_type_count": constraint_counts,
                    "by_type": constraints_by_type,
                    "details": constraints_info,
                },
                "message": f"Найдено {len(constraints_info)} ограничений для таблицы {target_schema}.{table_name}",
            }

        except Exception as e:
            logger.error(f"Ошибка при получении информации об ограничениях: {str(e)}")
            return {
                "success": False,
                "error": f"Ошибка при получении информации об ограничениях: {str(e)}",
            }

    def get_table_indexes(
        self, table_name: str, schema_name: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Получает информацию об индексах таблицы.

        Args:
            table_name: Имя таблицы
            schema_name: Имя схемы. Если None, используется текущая схема

        Returns:
            Dict[str, Any]: Информация об индексах таблицы
        """
        try:
            if self.connector is None:
                return {
                    "success": False,
                    "error": "Нет активного соединения с базой данных",
                }

            # Определяем схему
            target_schema = schema_name or self.connector.get_current_schema()

            # Получаем информацию об индексах
            indexes_info = self.connector.get_indexes_info(table_name, target_schema)

            # Группируем индексы по типам
            indexes_by_type = {}
            unique_indexes = []

            for index in indexes_info:
                index_type = index.get("index_type", "UNKNOWN")
                if index_type not in indexes_by_type:
                    indexes_by_type[index_type] = []
                indexes_by_type[index_type].append(index)

                if index.get("uniqueness") == "UNIQUE":
                    unique_indexes.append(index)

            # Подсчитываем статистику
            index_counts = {
                itype: len(indexes) for itype, indexes in indexes_by_type.items()
            }

            return {
                "success": True,
                "table_name": table_name,
                "schema_name": target_schema,
                "indexes": {
                    "total_count": len(indexes_info),
                    "unique_count": len(unique_indexes),
                    "by_type_count": index_counts,
                    "by_type": indexes_by_type,
                    "unique_indexes": unique_indexes,
                    "details": indexes_info,
                },
                "message": f"Найдено {len(indexes_info)} индексов для таблицы {target_schema}.{table_name}",
            }

        except Exception as e:
            logger.error(f"Ошибка при получении информации об индексах: {str(e)}")
            return {
                "success": False,
                "error": f"Ошибка при получении информации об индексах: {str(e)}",
            }

    def get_table_dependencies(
        self, table_name: str, schema_name: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Получает информацию о зависимостях таблицы (внешние ключи).

        Args:
            table_name: Имя таблицы
            schema_name: Имя схемы. Если None, используется текущая схема

        Returns:
            Dict[str, Any]: Информация о зависимостях таблицы
        """
        try:
            if self.connector is None:
                return {
                    "success": False,
                    "error": "Нет активного соединения с базой данных",
                }

            # Определяем схему
            target_schema = schema_name or self.connector.get_current_schema()

            # Получаем информацию о зависимостях
            dependencies_info = self.connector.get_dependencies_info(
                table_name, target_schema
            )

            # Разделяем на исходящие и входящие зависимости
            outgoing_dependencies = []  # Таблицы, на которые ссылается данная таблица
            incoming_dependencies = []  # Таблицы, которые ссылаются на данную таблицу

            for dep in dependencies_info:
                if dep.get("table_name") == table_name:
                    outgoing_dependencies.append(dep)
                else:
                    incoming_dependencies.append(dep)

            return {
                "success": True,
                "table_name": table_name,
                "schema_name": target_schema,
                "dependencies": {
                    "total_count": len(dependencies_info),
                    "outgoing_count": len(outgoing_dependencies),
                    "incoming_count": len(incoming_dependencies),
                    "outgoing": outgoing_dependencies,
                    "incoming": incoming_dependencies,
                    "all": dependencies_info,
                },
                "message": f"Найдено {len(dependencies_info)} зависимостей для таблицы {target_schema}.{table_name}",
            }

        except Exception as e:
            logger.error(f"Ошибка при получении информации о зависимостях: {str(e)}")
            return {
                "success": False,
                "error": f"Ошибка при получении информации о зависимостях: {str(e)}",
            }

    def get_table_sample_data(
        self, table_name: str, schema_name: Optional[str] = None, limit: int = 10
    ) -> Dict[str, Any]:
        """
        Получает образец данных из таблицы.

        Args:
            table_name: Имя таблицы
            schema_name: Имя схемы. Если None, используется текущая схема
            limit: Количество строк для выборки (максимум 100)

        Returns:
            Dict[str, Any]: Образец данных из таблицы
        """
        try:
            if self.connector is None:
                return {
                    "success": False,
                    "error": "Нет активного соединения с базой данных",
                }

            # Ограничиваем количество строк
            if limit > 100:
                limit = 100

            # Определяем схему
            target_schema = schema_name or self.connector.get_current_schema()

            # Формируем запрос
            if target_schema:
                full_table_name = f"{target_schema}.{table_name}"
            else:
                full_table_name = table_name

            # Выполняем запрос через безопасный метод
            query = f"SELECT * FROM {full_table_name}"
            result = self.database_tools.execute_safe_query(query, limit)

            if not result.get("success"):
                return result

            data = result.get("data", [])

            # Получаем информацию о столбцах для контекста
            columns_info = self.connector.get_column_info(table_name, target_schema)
            column_names = [col.get("column_name") for col in columns_info]

            return {
                "success": True,
                "table_name": table_name,
                "schema_name": target_schema,
                "sample_data": {
                    "columns": column_names,
                    "row_count": len(data),
                    "limit_applied": limit,
                    "data": data,
                },
                "message": f"Получено {len(data)} строк образца данных из таблицы {target_schema}.{table_name}",
            }

        except Exception as e:
            logger.error(f"Ошибка при получении образца данных: {str(e)}")
            return {
                "success": False,
                "error": f"Ошибка при получении образца данных: {str(e)}",
            }

    def analyze_table_relationships(
        self, table_name: str, schema_name: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Анализирует связи таблицы с другими таблицами.

        Args:
            table_name: Имя таблицы
            schema_name: Имя схемы. Если None, используется текущая схема

        Returns:
            Dict[str, Any]: Анализ связей таблицы
        """
        try:
            if self.connector is None:
                return {
                    "success": False,
                    "error": "Нет активного соединения с базой данных",
                }

            # Определяем схему
            target_schema = schema_name or self.connector.get_current_schema()

            # Получаем зависимости
            dependencies_info = self.connector.get_dependencies_info(
                table_name, target_schema
            )

            # Анализируем связи
            parent_tables = set()  # Таблицы, на которые ссылается данная таблица
            child_tables = set()  # Таблицы, которые ссылаются на данную таблицу

            for dep in dependencies_info:
                if dep.get("table_name") == table_name:
                    # Исходящая связь - данная таблица ссылается на другую
                    parent_tables.add(dep.get("referenced_table_name"))
                else:
                    # Входящая связь - другая таблица ссылается на данную
                    child_tables.add(dep.get("table_name"))

            # Определяем тип таблицы в иерархии
            table_type = "standalone"
            if parent_tables and child_tables:
                table_type = "intermediate"
            elif parent_tables and not child_tables:
                table_type = "leaf"
            elif not parent_tables and child_tables:
                table_type = "root"

            return {
                "success": True,
                "table_name": table_name,
                "schema_name": target_schema,
                "relationships": {
                    "table_type": table_type,
                    "parent_tables": sorted(list(parent_tables)),
                    "child_tables": sorted(list(child_tables)),
                    "parent_count": len(parent_tables),
                    "child_count": len(child_tables),
                    "total_relationships": len(dependencies_info),
                },
                "details": dependencies_info,
                "message": f"Таблица {target_schema}.{table_name} имеет тип '{table_type}' с {len(parent_tables)} родительскими и {len(child_tables)} дочерними таблицами",
            }

        except Exception as e:
            logger.error(f"Ошибка при анализе связей таблицы: {str(e)}")
            return {
                "success": False,
                "error": f"Ошибка при анализе связей таблицы: {str(e)}",
            }
