# 🔗 Database Explorer MCP - Интеграция с ИИ-редакторами

Комплексное решение для интеграции Database Explorer MCP Server с различными ИИ-редакторами кода.

## 📁 Структура директории

```
integrations/
├── README.md                    # Этот файл
├── INTEGRATION_GUIDE.md         # Полное руководство по интеграции
├── USAGE_EXAMPLES.md            # Примеры использования
├── universal_installer.py       # Универсальный установщик
├── test_integration.py          # Тестирование интеграции
├── cursor/                      # Интеграция с Cursor IDE
│   ├── check_mcp_support.py
│   ├── install_cursor.py
│   └── cursor_mcp_config.json
└── augment/                     # Интеграция с Augment Code
    ├── install_augment.py
    └── augment_mcp_config.json
```

## 🚀 Быстрый старт

### 1. Универсальная установка
```bash
# Автоматическое определение и установка для доступных редакторов
python integrations/universal_installer.py
```

### 2. Специфичная установка

#### Claude Desktop
```bash
python install_mcp.py
```

#### Cursor IDE
```bash
python integrations/cursor/install_cursor.py
```

#### Augment Code
```bash
python integrations/augment/install_augment.py
```

## 🧪 Тестирование

### Комплексное тестирование
```bash
python integrations/test_integration.py
```

### Проверка конкретного редактора
```bash
# Проверка поддержки MCP в Cursor
python integrations/cursor/check_mcp_support.py
```

## 📚 Документация

- **[INTEGRATION_GUIDE.md](INTEGRATION_GUIDE.md)** - Подробное руководство по интеграции
- **[USAGE_EXAMPLES.md](USAGE_EXAMPLES.md)** - Практические примеры использования

## 🎯 Поддерживаемые редакторы

| Редактор | Статус | Установщик | Примечания |
|----------|--------|------------|------------|
| **Claude Desktop** | ✅ Полная поддержка | `install_mcp.py` | Нативная поддержка MCP |
| **Cursor IDE** | ✅ Поддерживается | `cursor/install_cursor.py` | Через настройки MCP |
| **Augment Code** | ✅ Поддерживается | `augment/install_augment.py` | Встроенная поддержка MCP |
| **VS Code + Continue** | ⚠️ Ручная настройка | Ручная | Через расширение Continue |
| **Windsurf** | ⚠️ Экспериментальная | Ручная | Зависит от версии |

## 🔧 Требования

### Общие требования
- Python 3.8+
- Database Explorer MCP Server
- Установленные зависимости: `pip install -r requirements_mcp.txt`

### Специфичные требования

#### Claude Desktop
- Установленный Claude Desktop
- Права на запись в конфигурационную директорию

#### Cursor IDE
- Cursor IDE с поддержкой MCP
- Доступ к настройкам пользователя

#### Augment Code
- Augment Code IDE
- Поддержка MCP протокола

## 📋 Пошаговая установка

### Шаг 1: Подготовка
```bash
# Клонирование и переход в директорию
cd Database_Explorer_MCP

# Установка зависимостей
pip install -r requirements_mcp.txt

# Настройка переменных окружения
cp .env.example .env
# Отредактируйте .env файл
```

### Шаг 2: Тестирование MCP сервера
```bash
python test_mcp_server.py
```

### Шаг 3: Выбор и установка для редактора
```bash
# Универсальная установка (рекомендуется)
python integrations/universal_installer.py

# Или специфичная установка
python install_mcp.py  # Claude Desktop
python integrations/cursor/install_cursor.py  # Cursor IDE
python integrations/augment/install_augment.py  # Augment Code
```

### Шаг 4: Проверка интеграции
```bash
python integrations/test_integration.py
```

### Шаг 5: Перезапуск редактора
Перезапустите выбранный ИИ-редактор для применения изменений.

## 💡 Примеры использования

### В Claude Desktop
```
Подключись к базе данных Oracle
Покажи все схемы в базе данных
Получи структуру таблицы USERS
```

### В Cursor IDE
```
@database-explorer подключись к БД
@database-explorer анализируй схему MAIN
@database-explorer создай модель для таблицы PRODUCTS
```

### В Augment Code
```
Исследуй структуру базы данных
Найди связи между таблицами заказов
Создай документацию для схемы INVENTORY
```

## 🔍 Диагностика проблем

### Общие проблемы

#### MCP сервер не запускается
```bash
# Проверка зависимостей
pip list | grep mcp

# Тест запуска
python mcp_server.py --help
```

#### Редактор не видит MCP сервер
1. Проверьте конфигурационные файлы
2. Убедитесь в правильности путей
3. Перезапустите редактор
4. Проверьте логи

#### Ошибки подключения к БД
```bash
# Проверка переменных окружения
python -c "import os; print('DB_TYPE:', os.getenv('DB_TYPE'))"

# Тест подключения
python -c "
from mcp_tools.database_tools import DatabaseTools
db = DatabaseTools()
print(db.connect_database('oracle'))
"
```

### Специфичные проблемы

#### Claude Desktop
- Проверьте `claude_desktop_config.json` на валидность
- Убедитесь в правильности путей к MCP серверу

#### Cursor IDE
- Обновите Cursor до последней версии
- Проверьте поддержку MCP в настройках

#### Augment Code
- Убедитесь, что `mcp_config.json` в правильной директории
- Проверьте логи Augment Code

## 📞 Поддержка

### Автоматическая диагностика
```bash
python integrations/test_integration.py
```

### Ручная проверка
```bash
# Проверка конфигураций
find ~ -name "*claude*config*" 2>/dev/null
find ~ -name "*cursor*settings*" 2>/dev/null
find ~ -name "*augment*config*" 2>/dev/null

# Проверка процессов
ps aux | grep -E "(claude|cursor|augment)"
```

### Создание отчета о проблеме
При создании issue включите:
- Результат `python integrations/test_integration.py`
- Версию Python: `python --version`
- ОС и версию
- Используемый редактор и версию
- Логи ошибок

## 🎉 Успешная интеграция

После успешной настройки вы получите:

- 🔍 **15 специализированных инструментов** для работы с БД
- 🤖 **Интеграцию с ИИ** для естественного взаимодействия
- 🔒 **Безопасное выполнение запросов** с валидацией
- 📊 **Анализ структуры БД** через разговорный интерфейс
- 📈 **Автоматическую документацию** и отчеты

## 🔄 Обновления

Для обновления интеграции:

```bash
# Обновление MCP сервера
git pull origin main

# Переустановка зависимостей
pip install -r requirements_mcp.txt --upgrade

# Повторная установка в редакторы
python integrations/universal_installer.py
```

## 📄 Лицензия

MIT License - см. основной файл LICENSE проекта.

---

**Database Explorer MCP** - делаем исследование баз данных доступным для ИИ! 🚀
