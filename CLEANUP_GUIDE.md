# 🧹 Руководство по очистке проекта Database Explorer MCP

Набор Windows batch файлов для безопасной очистки и управления проектом Database Explorer MCP.

## 📁 Файлы очистки

| Файл | Назначение | Безопасность |
|------|------------|--------------|
| `cleanup_project.bat` | Основная очистка проекта | ✅ Безопасно (перемещение) |
| `restore_project.bat` | Восстановление файлов | ✅ Безопасно (восстановление) |
| `delete_cleanup.bat` | Окончательное удаление | ⚠️ Необратимо |

## 🚀 Быстрый старт

### 1. Основная очистка
```cmd
cleanup_project.bat
```

### 2. При необходимости восстановления
```cmd
restore_project.bat
```

### 3. Окончательное удаление (опционально)
```cmd
delete_cleanup.bat
```

## 📋 Подробное описание

### 🧹 cleanup_project.bat

**Назначение:** Безопасная очистка проекта от ненужных файлов

**Что делает:**
- ✅ Создает папку `to_delete`
- ✅ Перемещает ненужные файлы (НЕ удаляет!)
- ✅ Сохраняет все важные компоненты
- ✅ Показывает подробный отчет

**Перемещаемые файлы:**
```
📄 Файлы анализа таблиц:
   • ffmaprep_analysis.md
   • supermag_*.md
   • *_analysis.md

📄 Старые конфигурации:
   • old_*.md
   • *_prompt.md
   • oracle_connection_prompt.md

🧪 Тестовые файлы:
   • my_test.py
   • test_connection_pool.py
   • test_drcp_connection.py
   • test_oracle_connection.py
   • query_*.py

🗂️ Кэш Python:
   • __pycache__/ (все папки)
   • *.pyc, *.pyo файлы

📊 Логи:
   • logs/ папка
   • *.log файлы

🐍 Виртуальное окружение:
   • venv/ папка

🗃️ Дополнительные файлы:
   • _requirements.txt
   • pdf_extractor.py
```

**Сохраняемые файлы:**
```
🔧 Основные модули:
   ✓ mcp_server.py
   ✓ db_explorer.py
   ✓ install_mcp.py
   ✓ test_mcp_server.py

📁 Директории с кодом:
   ✓ mcp_tools/
   ✓ connectors/
   ✓ exporters/
   ✓ models/
   ✓ integrations/

⚙️ Конфигурация:
   ✓ .env.example
   ✓ requirements.txt
   ✓ requirements_mcp.txt
   ✓ config_db.py

📚 Документация:
   ✓ README*.md
   ✓ QUICKSTART.md
   ✓ INTEGRATION_GUIDE.md
   ✓ USAGE_EXAMPLES.md
```

### 🔄 restore_project.bat

**Назначение:** Восстановление файлов из папки `to_delete`

**Когда использовать:**
- ❌ Случайно переместили нужный файл
- ❌ Нужно вернуть что-то из удаленного
- ❌ Хотите отменить очистку

**Что делает:**
- ✅ Проверяет наличие папки `to_delete`
- ✅ Показывает количество файлов для восстановления
- ✅ Запрашивает подтверждение
- ✅ Перемещает файлы обратно в проект
- ✅ Удаляет пустую папку `to_delete`

**Безопасность:**
- ⚠️ Может перезаписать существующие файлы
- ✅ Запрашивает подтверждение
- ✅ Показывает подробный отчет

### 🔥 delete_cleanup.bat

**Назначение:** Окончательное удаление папки `to_delete`

**⚠️ ВНИМАНИЕ:** Операция необратима!

**Когда использовать:**
- ✅ Убедились, что все нужные файлы сохранены
- ✅ Прошло время, и файлы точно не нужны
- ✅ Хотите освободить место на диске

**Что делает:**
- 🔍 Показывает содержимое папки `to_delete`
- ⚠️ Выводит множественные предупреждения
- 🔐 Требует двойное подтверждение
- 🔥 Окончательно удаляет папку и содержимое

**Меры безопасности:**
- ⚠️ Множественные предупреждения
- 🔐 Двойное подтверждение
- 📋 Показ содержимого перед удалением
- 💡 Предложение альтернатив

## 🎯 Рекомендуемый порядок действий

### Шаг 1: Подготовка
```cmd
# Убедитесь, что находитесь в корне проекта
dir mcp_server.py
```

### Шаг 2: Резервное копирование (опционально)
```cmd
# Создайте резервную копию важных файлов
xcopy *.py backup\ /s /i
```

### Шаг 3: Очистка
```cmd
cleanup_project.bat
```

### Шаг 4: Проверка
```cmd
# Проверьте, что проект работает
python test_mcp_server.py
```

### Шаг 5: Решение о папке to_delete
```cmd
# Вариант A: Оставить как резерв (рекомендуется)
# Ничего не делать

# Вариант B: Восстановить что-то нужное
restore_project.bat

# Вариант C: Удалить окончательно (через время)
delete_cleanup.bat
```

## 🔍 Проверка результатов

### После очистки проект должен содержать:
```
Database_Explorer_MCP/
├── mcp_server.py              ✓ Основной MCP сервер
├── db_explorer.py             ✓ CLI интерфейс
├── install_mcp.py             ✓ Установщик
├── test_mcp_server.py         ✓ Тестирование
├── config_db.py               ✓ Конфигурация БД
├── .env.example               ✓ Пример настроек
├── requirements.txt           ✓ Зависимости CLI
├── requirements_mcp.txt       ✓ Зависимости MCP
├── README.md                  ✓ Основная документация
├── README_MCP.md              ✓ Документация MCP
├── QUICKSTART.md              ✓ Быстрый старт
├── mcp_tools/                 ✓ Инструменты MCP
├── connectors/                ✓ Коннекторы БД
├── exporters/                 ✓ Экспортеры
├── models/                    ✓ Модели данных
├── integrations/              ✓ Интеграции с редакторами
└── to_delete/                 ✓ Перемещенные файлы
```

### Тестирование после очистки:
```cmd
# Тест MCP сервера
python test_mcp_server.py

# Тест CLI интерфейса
python db_explorer.py --help

# Тест установщика
python install_mcp.py --help
```

## ⚠️ Важные предупреждения

### ❌ НЕ запускайте скрипты если:
- Не находитесь в корне проекта Database Explorer MCP
- Не создали резервную копию важных данных
- Не уверены в том, что делаете

### ✅ Безопасные действия:
- `cleanup_project.bat` - всегда безопасен (только перемещение)
- `restore_project.bat` - безопасен (восстановление)

### ⚠️ Потенциально опасные действия:
- `delete_cleanup.bat` - необратимое удаление

## 🆘 Восстановление после проблем

### Если что-то пошло не так:

1. **Файлы случайно перемещены:**
   ```cmd
   restore_project.bat
   ```

2. **Проект не работает после очистки:**
   ```cmd
   # Восстановите все
   restore_project.bat
   
   # Проверьте зависимости
   pip install -r requirements_mcp.txt
   
   # Протестируйте
   python test_mcp_server.py
   ```

3. **Случайно удалили to_delete:**
   ```cmd
   # Восстановите из резервной копии
   xcopy backup\*.* . /s /y
   ```

## 💡 Советы по использованию

### Лучшие практики:
1. **Всегда создавайте резервную копию** перед очисткой
2. **Тестируйте проект** после очистки
3. **Не удаляйте to_delete сразу** - подождите несколько дней
4. **Проверяйте содержимое to_delete** перед удалением

### Когда НЕ использовать очистку:
- 🚫 В production окружении
- 🚫 Если не уверены в содержимом проекта
- 🚫 Перед важной демонстрацией
- 🚫 Если нет резервной копии

### Когда использовать очистку:
- ✅ Перед коммитом в Git
- ✅ При подготовке релиза
- ✅ Для освобождения места
- ✅ При передаче проекта другим разработчикам

## 🎉 Результат

После успешной очистки вы получите:
- 🧹 **Чистый проект** без временных файлов
- 📦 **Компактный размер** для передачи
- 🚀 **Готовый к использованию** MCP сервер
- 📚 **Полную документацию**
- 🔧 **Все необходимые инструменты**

Наслаждайтесь чистым и организованным проектом Database Explorer MCP! 🎯
