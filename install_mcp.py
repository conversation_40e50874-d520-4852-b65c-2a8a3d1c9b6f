#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Скрипт для установки Database Explorer MCP Server в Claude Desktop.
"""

import os
import json
import platform
from pathlib import Path
from typing import Dict, Any


def get_claude_config_path() -> Path:
    """
    Получает путь к конфигурационному файлу Claude Desktop.
    
    Returns:
        Path: Путь к файлу конфигурации
    """
    system = platform.system()
    
    if system == "Windows":
        # Windows: %APPDATA%\Claude\claude_desktop_config.json
        appdata = os.getenv("APPDATA")
        if appdata:
            return Path(appdata) / "Claude" / "claude_desktop_config.json"
        else:
            raise Exception("Переменная окружения APPDATA не найдена")
    
    elif system == "Darwin":  # macOS
        # macOS: ~/Library/Application Support/Claude/claude_desktop_config.json
        home = Path.home()
        return home / "Library" / "Application Support" / "Claude" / "claude_desktop_config.json"
    
    elif system == "Linux":
        # Linux: ~/.config/Claude/claude_desktop_config.json
        home = Path.home()
        return home / ".config" / "Claude" / "claude_desktop_config.json"
    
    else:
        raise Exception(f"Неподдерживаемая операционная система: {system}")


def load_claude_config(config_path: Path) -> Dict[str, Any]:
    """
    Загружает существующую конфигурацию Claude Desktop.
    
    Args:
        config_path: Путь к файлу конфигурации
        
    Returns:
        Dict[str, Any]: Конфигурация Claude Desktop
    """
    if config_path.exists():
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except json.JSONDecodeError:
            print(f"⚠️  Файл конфигурации {config_path} поврежден, создается новый")
            return {}
        except Exception as e:
            print(f"⚠️  Ошибка при чтении конфигурации: {e}")
            return {}
    else:
        print(f"📝 Файл конфигурации {config_path} не существует, создается новый")
        return {}


def save_claude_config(config_path: Path, config: Dict[str, Any]) -> None:
    """
    Сохраняет конфигурацию Claude Desktop.
    
    Args:
        config_path: Путь к файлу конфигурации
        config: Конфигурация для сохранения
    """
    # Создаем директорию если она не существует
    config_path.parent.mkdir(parents=True, exist_ok=True)
    
    with open(config_path, 'w', encoding='utf-8') as f:
        json.dump(config, f, indent=2, ensure_ascii=False)


def create_mcp_server_config(
    server_name: str = "database-explorer",
    transport: str = "stdio",
    server_path: str = None,
    env_vars: Dict[str, str] = None
) -> Dict[str, Any]:
    """
    Создает конфигурацию для MCP сервера.
    
    Args:
        server_name: Имя сервера в конфигурации
        transport: Тип транспорта (stdio или sse)
        server_path: Путь к серверу
        env_vars: Переменные окружения
        
    Returns:
        Dict[str, Any]: Конфигурация MCP сервера
    """
    if server_path is None:
        server_path = str(Path(__file__).parent.absolute() / "mcp_server.py")
    
    if env_vars is None:
        env_vars = {}
    
    if transport == "stdio":
        config = {
            "command": "python",
            "args": [server_path],
            "env": env_vars
        }
    elif transport == "sse":
        config = {
            "transport": "sse",
            "url": "http://localhost:8000/sse"
        }
    else:
        raise ValueError(f"Неподдерживаемый тип транспорта: {transport}")
    
    return config


def install_mcp_server():
    """
    Устанавливает Database Explorer MCP Server в Claude Desktop.
    """
    print("🚀 Установка Database Explorer MCP Server в Claude Desktop")
    print("=" * 60)
    
    try:
        # Получаем путь к конфигурации Claude
        config_path = get_claude_config_path()
        print(f"📁 Путь к конфигурации Claude: {config_path}")
        
        # Загружаем существующую конфигурацию
        config = load_claude_config(config_path)
        
        # Инициализируем секцию mcpServers если её нет
        if "mcpServers" not in config:
            config["mcpServers"] = {}
        
        # Получаем параметры от пользователя
        print("\n⚙️  Настройка MCP сервера:")
        
        server_name = input("Имя сервера [database-explorer]: ").strip() or "database-explorer"
        
        print("\nВыберите тип транспорта:")
        print("1. stdio (рекомендуется)")
        print("2. sse")
        transport_choice = input("Введите номер (1 или 2) [1]: ").strip() or "1"
        transport = "stdio" if transport_choice == "1" else "sse"
        
        # Настройка переменных окружения для stdio
        env_vars = {}
        if transport == "stdio":
            print("\n🔧 Настройка подключения к базе данных:")
            print("(Оставьте пустым для использования значений по умолчанию)")
            
            db_type = input("Тип БД [oracle]: ").strip() or "oracle"
            env_vars["DB_TYPE"] = db_type
            
            db_host = input("Хост БД: ").strip()
            if db_host:
                env_vars["DB_HOST"] = db_host
            
            db_port = input("Порт БД: ").strip()
            if db_port:
                env_vars["DB_PORT"] = db_port
            
            db_user = input("Пользователь БД: ").strip()
            if db_user:
                env_vars["DB_USER"] = db_user
            
            db_password = input("Пароль БД: ").strip()
            if db_password:
                env_vars["DB_PASSWORD"] = db_password
            
            if db_type.lower() == "oracle":
                db_service = input("Сервис Oracle: ").strip()
                if db_service:
                    env_vars["DB_SERVICE"] = db_service
            elif db_type.lower() in ["mysql", "postgresql"]:
                db_name = input("Имя БД: ").strip()
                if db_name:
                    env_vars["DB_NAME"] = db_name
        
        # Создаем конфигурацию сервера
        server_config = create_mcp_server_config(
            server_name=server_name,
            transport=transport,
            env_vars=env_vars
        )
        
        # Добавляем сервер в конфигурацию
        config["mcpServers"][server_name] = server_config
        
        # Сохраняем конфигурацию
        save_claude_config(config_path, config)
        
        print(f"\n✅ MCP сервер '{server_name}' успешно установлен!")
        print(f"📁 Конфигурация сохранена в: {config_path}")
        
        print("\n📋 Конфигурация сервера:")
        print(json.dumps({server_name: server_config}, indent=2, ensure_ascii=False))
        
        print("\n🔄 Перезапустите Claude Desktop для применения изменений")
        
        # Проверяем существование файла сервера
        server_path = Path(__file__).parent.absolute() / "mcp_server.py"
        if not server_path.exists():
            print(f"\n⚠️  Внимание: Файл сервера не найден по пути {server_path}")
            print("   Убедитесь, что mcp_server.py находится в той же директории")
        
    except Exception as e:
        print(f"\n❌ Ошибка при установке: {e}")
        return False
    
    return True


def uninstall_mcp_server():
    """
    Удаляет Database Explorer MCP Server из Claude Desktop.
    """
    print("🗑️  Удаление Database Explorer MCP Server из Claude Desktop")
    print("=" * 60)
    
    try:
        # Получаем путь к конфигурации Claude
        config_path = get_claude_config_path()
        
        # Загружаем существующую конфигурацию
        config = load_claude_config(config_path)
        
        if "mcpServers" not in config:
            print("❌ Секция mcpServers не найдена в конфигурации")
            return False
        
        # Показываем доступные серверы
        servers = list(config["mcpServers"].keys())
        if not servers:
            print("❌ MCP серверы не найдены в конфигурации")
            return False
        
        print("Найдены следующие MCP серверы:")
        for i, server in enumerate(servers, 1):
            print(f"{i}. {server}")
        
        # Выбираем сервер для удаления
        choice = input("\nВведите номер сервера для удаления или имя сервера: ").strip()
        
        server_to_remove = None
        if choice.isdigit():
            index = int(choice) - 1
            if 0 <= index < len(servers):
                server_to_remove = servers[index]
        else:
            if choice in servers:
                server_to_remove = choice
        
        if server_to_remove is None:
            print("❌ Неверный выбор сервера")
            return False
        
        # Удаляем сервер
        del config["mcpServers"][server_to_remove]
        
        # Сохраняем конфигурацию
        save_claude_config(config_path, config)
        
        print(f"✅ MCP сервер '{server_to_remove}' успешно удален!")
        print("🔄 Перезапустите Claude Desktop для применения изменений")
        
    except Exception as e:
        print(f"❌ Ошибка при удалении: {e}")
        return False
    
    return True


def main():
    """
    Главная функция для управления установкой MCP сервера.
    """
    print("Database Explorer MCP Server - Установщик")
    print("=" * 50)
    
    print("Выберите действие:")
    print("1. Установить MCP сервер")
    print("2. Удалить MCP сервер")
    print("3. Показать текущую конфигурацию")
    
    choice = input("Введите номер (1, 2 или 3): ").strip()
    
    if choice == "1":
        install_mcp_server()
    elif choice == "2":
        uninstall_mcp_server()
    elif choice == "3":
        try:
            config_path = get_claude_config_path()
            config = load_claude_config(config_path)
            print(f"\n📁 Конфигурация Claude Desktop ({config_path}):")
            print(json.dumps(config, indent=2, ensure_ascii=False))
        except Exception as e:
            print(f"❌ Ошибка при чтении конфигурации: {e}")
    else:
        print("❌ Неверный выбор")


if __name__ == "__main__":
    main()
