#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Скрипт для тестирования интеграции Database Explorer MCP с различными редакторами.
"""

import asyncio
import json
import os
import platform
import subprocess
import time
from pathlib import Path
from typing import Dict, Any, List


class IntegrationTester:
    """Тестировщик интеграции MCP с редакторами."""
    
    def __init__(self):
        self.current_dir = Path(__file__).parent.parent.absolute()
        self.mcp_server_path = self.current_dir / "mcp_server.py"
        self.test_results = {}
    
    def test_mcp_server_standalone(self) -> bool:
        """Тестирует MCP сервер в автономном режиме."""
        print("🧪 Тестирование MCP сервера...")
        
        try:
            # Запускаем тест сервера
            result = subprocess.run([
                "python", str(self.current_dir / "test_mcp_server.py")
            ], input="2\n", text=True, capture_output=True, timeout=30)
            
            if result.returncode == 0 and "✅ Список инструментов получен успешно!" in result.stdout:
                print("✅ MCP сервер работает корректно")
                return True
            else:
                print("❌ MCP сервер не работает")
                print(f"Вывод: {result.stdout}")
                print(f"Ошибки: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            print("❌ Тайм-аут при тестировании MCP сервера")
            return False
        except Exception as e:
            print(f"❌ Ошибка при тестировании MCP сервера: {e}")
            return False
    
    def test_claude_desktop_config(self) -> bool:
        """Тестирует конфигурацию Claude Desktop."""
        print("🧪 Тестирование конфигурации Claude Desktop...")
        
        try:
            system = platform.system()
            if system == "Windows":
                appdata = os.getenv("APPDATA")
                config_path = Path(appdata) / "Claude" / "claude_desktop_config.json"
            elif system == "Darwin":
                config_path = Path.home() / "Library/Application Support/Claude/claude_desktop_config.json"
            elif system == "Linux":
                config_path = Path.home() / ".config/Claude/claude_desktop_config.json"
            else:
                print("❌ Неподдерживаемая ОС")
                return False
            
            if not config_path.exists():
                print("❌ Конфигурационный файл Claude Desktop не найден")
                return False
            
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            if "mcpServers" in config and "database-explorer" in config["mcpServers"]:
                print("✅ Конфигурация Claude Desktop найдена")
                server_config = config["mcpServers"]["database-explorer"]
                
                # Проверяем путь к серверу
                if "args" in server_config and server_config["args"]:
                    server_path = Path(server_config["args"][0])
                    if server_path.exists():
                        print("✅ Путь к MCP серверу корректный")
                        return True
                    else:
                        print(f"❌ MCP сервер не найден по пути: {server_path}")
                        return False
                else:
                    print("❌ Некорректная конфигурация сервера")
                    return False
            else:
                print("❌ Database Explorer не найден в конфигурации")
                return False
                
        except Exception as e:
            print(f"❌ Ошибка при проверке конфигурации Claude Desktop: {e}")
            return False
    
    def test_cursor_config(self) -> bool:
        """Тестирует конфигурацию Cursor IDE."""
        print("🧪 Тестирование конфигурации Cursor IDE...")
        
        try:
            system = platform.system()
            if system == "Windows":
                appdata = os.getenv("APPDATA")
                config_path = Path(appdata) / "Cursor" / "User" / "settings.json"
            elif system == "Darwin":
                config_path = Path.home() / "Library/Application Support/Cursor/User/settings.json"
            elif system == "Linux":
                config_path = Path.home() / ".config/Cursor/User/settings.json"
            else:
                print("❌ Неподдерживаемая ОС")
                return False
            
            if not config_path.exists():
                print("❌ Конфигурационный файл Cursor не найден")
                return False
            
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            # Ищем MCP настройки
            mcp_keys = [key for key in config.keys() if 'mcp' in key.lower()]
            
            if mcp_keys:
                print("✅ MCP настройки найдены в Cursor")
                return True
            else:
                print("❌ MCP настройки не найдены в Cursor")
                return False
                
        except Exception as e:
            print(f"❌ Ошибка при проверке конфигурации Cursor: {e}")
            return False
    
    def test_augment_config(self) -> bool:
        """Тестирует конфигурацию Augment Code."""
        print("🧪 Тестирование конфигурации Augment Code...")
        
        try:
            system = platform.system()
            if system == "Windows":
                appdata = os.getenv("APPDATA")
                config_path = Path(appdata) / "Augment" / "mcp_config.json"
            elif system == "Darwin":
                config_path = Path.home() / "Library/Application Support/Augment/mcp_config.json"
            elif system == "Linux":
                config_path = Path.home() / ".config/Augment/mcp_config.json"
            else:
                print("❌ Неподдерживаемая ОС")
                return False
            
            if not config_path.exists():
                print("❌ Конфигурационный файл Augment не найден")
                return False
            
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            if "mcpServers" in config and "database-explorer" in config["mcpServers"]:
                print("✅ Конфигурация Augment Code найдена")
                return True
            else:
                print("❌ Database Explorer не найден в конфигурации Augment")
                return False
                
        except Exception as e:
            print(f"❌ Ошибка при проверке конфигурации Augment: {e}")
            return False
    
    def test_environment_variables(self) -> bool:
        """Тестирует переменные окружения."""
        print("🧪 Тестирование переменных окружения...")
        
        required_vars = ["DB_TYPE"]
        optional_vars = ["DB_HOST", "DB_PORT", "DB_USER", "DB_PASSWORD", "DB_SERVICE", "DB_NAME"]
        
        missing_required = []
        missing_optional = []
        
        for var in required_vars:
            if not os.getenv(var):
                missing_required.append(var)
        
        for var in optional_vars:
            if not os.getenv(var):
                missing_optional.append(var)
        
        if missing_required:
            print(f"❌ Отсутствуют обязательные переменные: {', '.join(missing_required)}")
            return False
        
        if missing_optional:
            print(f"⚠️  Отсутствуют опциональные переменные: {', '.join(missing_optional)}")
            print("💡 Убедитесь, что настроен .env файл или переменные окружения")
        
        print("✅ Переменные окружения настроены")
        return True
    
    def run_comprehensive_test(self) -> Dict[str, bool]:
        """Запускает комплексное тестирование."""
        print("🚀 Комплексное тестирование интеграции Database Explorer MCP")
        print("=" * 70)
        
        tests = {
            "MCP Server": self.test_mcp_server_standalone,
            "Environment Variables": self.test_environment_variables,
            "Claude Desktop Config": self.test_claude_desktop_config,
            "Cursor IDE Config": self.test_cursor_config,
            "Augment Code Config": self.test_augment_config
        }
        
        results = {}
        
        for test_name, test_func in tests.items():
            print(f"\n{'='*20} {test_name} {'='*20}")
            try:
                results[test_name] = test_func()
            except Exception as e:
                print(f"❌ Ошибка в тесте {test_name}: {e}")
                results[test_name] = False
        
        # Выводим итоги
        print("\n" + "="*70)
        print("📊 ИТОГИ ТЕСТИРОВАНИЯ")
        print("="*70)
        
        passed = 0
        total = len(results)
        
        for test_name, result in results.items():
            status = "✅ ПРОЙДЕН" if result else "❌ НЕ ПРОЙДЕН"
            print(f"{test_name:.<30} {status}")
            if result:
                passed += 1
        
        print(f"\nОбщий результат: {passed}/{total} тестов пройдено")
        
        if passed == total:
            print("🎉 Все тесты пройдены! Интеграция настроена корректно.")
        elif passed > 0:
            print("⚠️  Частичная интеграция. Проверьте неудачные тесты.")
        else:
            print("❌ Интеграция не настроена. Требуется настройка.")
        
        return results
    
    def generate_troubleshooting_report(self, results: Dict[str, bool]):
        """Генерирует отчет по устранению неполадок."""
        failed_tests = [test for test, result in results.items() if not result]
        
        if not failed_tests:
            return
        
        print("\n🔧 РЕКОМЕНДАЦИИ ПО УСТРАНЕНИЮ НЕПОЛАДОК")
        print("="*50)
        
        for test in failed_tests:
            print(f"\n❌ {test}:")
            
            if test == "MCP Server":
                print("   • Проверьте, что установлены все зависимости: pip install -r requirements_mcp.txt")
                print("   • Убедитесь, что Python 3.8+ установлен")
                print("   • Запустите: python test_mcp_server.py")
            
            elif test == "Environment Variables":
                print("   • Создайте .env файл из .env.example")
                print("   • Заполните переменные подключения к БД")
                print("   • Проверьте доступность базы данных")
            
            elif test == "Claude Desktop Config":
                print("   • Запустите: python install_mcp.py")
                print("   • Перезапустите Claude Desktop")
                print("   • Проверьте путь к mcp_server.py в конфигурации")
            
            elif test == "Cursor IDE Config":
                print("   • Запустите: python integrations/cursor/install_cursor.py")
                print("   • Убедитесь, что Cursor поддерживает MCP")
                print("   • Перезапустите Cursor IDE")
            
            elif test == "Augment Code Config":
                print("   • Запустите: python integrations/augment/install_augment.py")
                print("   • Перезапустите Augment Code")
                print("   • Проверьте логи MCP в Augment")


def main():
    """Главная функция."""
    tester = IntegrationTester()
    results = tester.run_comprehensive_test()
    tester.generate_troubleshooting_report(results)


if __name__ == "__main__":
    main()
