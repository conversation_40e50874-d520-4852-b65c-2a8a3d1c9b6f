from qdrant_client import QdrantClient
from sentence_transformers import SentenceTransformer
from loguru import logger

from config_db import QDRANT_HOST

# Инициализация логгера
logger.add(
    "logs/my_test.log",
    rotation="100 MB",
    retention="30 days",
    format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {function}:{line} | {message}",
    level="INFO"
)

def search_in_qdrant(query_text, host=QDRANT_HOST, port=6333, collection_name="documents", limit=5):
    """
    Поиск похожих документов в Qdrant
    Args:
        query_text: Текст для поиска
        host: Хост Qdrant сервера
        port: Порт Qdrant сервера
        collection_name: Имя коллекции
        limit: Количество результатов
    Returns:
        list: Список найденных документов с их степенью схожести
    """
    try:
        # Подключение к серверу Qdrant
        client = QdrantClient(host=host, port=port)
        logger.info("Успешное подключение к Qdrant серверу")

        # Инициализация модели для создания эмбеддингов
        model = SentenceTransformer('all-MiniLM-L6-v2')
        
        # Векторизация поискового запроса
        query_vector = model.encode(query_text).tolist()
        
        # Поиск похожих документов
        search_results = client.search(
            collection_name=collection_name,
            query_vector=query_vector,
            limit=limit
        )
        
        # Форматирование и вывод результатов
        logger.info(f"\nРезультаты поиска для запроса: '{query_text}'")
        logger.info("-" * 50)
        
        formatted_results = []
        for idx, result in enumerate(search_results, 1):
            similarity = result.score * 100  # Преобразование в проценты
            text = result.payload.get('text', 'Текст не найден')
            chunk_index = result.payload.get('chunk_index', 'Индекс не указан')
            
            result_info = {
                'rank': idx,
                'similarity': similarity,
                'text': text,
                'chunk_index': chunk_index
            }
            formatted_results.append(result_info)
            
            logger.info(f"Результат #{idx}")
            logger.info(f"Степень схожести: {similarity:.2f}%")
            logger.info(f"Индекс чанка: {chunk_index}")
            logger.info(f"Текст: {text}")
            logger.info("-" * 50)
            
        return formatted_results
        
    except Exception as e:
        logger.error(f"Ошибка при поиске в Qdrant: {str(e)}")
        return []

if __name__ == "__main__":
    # Примеры поисковых запросов
    search_queries = [
        "smgoods",
        
    ]
    
    for query in search_queries:
        results = search_in_qdrant(query)
        logger.info(f"\nОбработан поисковый запрос: '{query}'\n")