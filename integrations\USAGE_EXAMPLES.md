# 💡 Примеры использования Database Explorer MCP

Практические примеры использования Database Explorer MCP в различных ИИ-редакторах.

## 🤖 Claude Desktop

### Базовые операции

#### Подключение к базе данных
```
Подключись к базе данных Oracle с хостом localhost, портом 1521, пользователем SYSTEM и сервисом ORCL
```

#### Исследование схем
```
Покажи все доступные схемы в базе данных
```

```
Получи детальную информацию о схеме HR включая количество таблиц и статистику
```

#### Анализ таблиц
```
Покажи структуру таблицы EMPLOYEES в схеме HR
```

```
Получи образец данных из таблицы DEPARTMENTS, лимит 10 строк
```

### Продвинутые сценарии

#### Анализ архитектуры БД
```
Проанализируй архитектуру базы данных:
1. Покажи все схемы
2. Для схемы HR найди все таблицы
3. Определи связи между таблицами EMPLOYEES и DEPARTMENTS
4. Покажи индексы на таблице EMPLOYEES
```

#### Исследование производительности
```
Найди таблицы с наибольшим количеством индексов в схеме HR и объясни зачем они нужны
```

#### Сравнение схем
```
Сравни схемы PROD и TEST, покажи какие таблицы есть только в одной из них
```

## 🎯 Cursor IDE

### Использование через команды

#### Подключение
```
@database-explorer подключись к Oracle базе данных
```

#### Исследование структуры
```
@database-explorer покажи все схемы и выбери самую интересную для анализа
```

```
@database-explorer получи полную структуру таблицы ORDERS включая все ограничения и индексы
```

### Интеграция с кодом

#### Генерация SQL
```
@database-explorer изучи структуру таблицы CUSTOMERS и создай SQL запрос для выборки активных клиентов
```

#### Создание моделей данных
```
@database-explorer проанализируй таблицы USERS, ORDERS, PRODUCTS и создай Python модели SQLAlchemy
```

#### Оптимизация запросов
```
@database-explorer изучи индексы на таблице TRANSACTIONS и предложи оптимизацию для запроса по дате
```

## 🎨 Augment Code

### Исследовательский анализ

#### Комплексный анализ схемы
```
Исследуй схему ECOMMERCE:
- Найди все таблицы
- Определи основные сущности (пользователи, заказы, товары)
- Построй карту связей между таблицами
- Найди потенциальные проблемы в структуре
```

#### Анализ качества данных
```
Проанализируй качество данных в схеме SALES:
- Найди таблицы без первичных ключей
- Определи столбцы без ограничений NOT NULL
- Покажи таблицы без индексов
- Предложи улучшения
```

### Документирование

#### Создание документации
```
Создай техническую документацию для схемы INVENTORY:
- Описание каждой таблицы
- Назначение столбцов
- Связи между таблицами
- Бизнес-правила из ограничений
```

#### Диаграммы связей
```
Построй диаграмму связей для таблиц заказов:
- CUSTOMERS -> ORDERS -> ORDER_ITEMS -> PRODUCTS
- Покажи типы связей (1:1, 1:N, N:M)
- Укажи ключевые поля
```

## 💻 VS Code с Continue

### Разработка приложений

#### Создание API
```
Изучи структуру таблиц USER_PROFILES и PREFERENCES, создай REST API endpoints на FastAPI
```

#### Миграции базы данных
```
Сравни текущую структуру таблицы PRODUCTS с требованиями и создай Alembic миграцию
```

#### Тестирование
```
Создай тестовые данные для таблиц CATEGORIES и PRODUCTS на основе их структуры
```

### Отладка и анализ

#### Поиск проблем
```
Найди таблицы в схеме APP без внешних ключей, которые должны их иметь
```

#### Анализ производительности
```
Изучи индексы на таблицах с префиксом LOG_ и предложи оптимизацию для запросов по времени
```

## 🌊 Windsurf

### Архитектурный анализ

#### Рефакторинг БД
```
Проанализируй схему LEGACY и предложи план рефакторинга:
- Нормализация таблиц
- Оптимизация индексов
- Улучшение связей
```

#### Планирование миграции
```
Создай план миграции данных из схемы OLD_SYSTEM в NEW_SYSTEM:
- Сопоставь таблицы
- Определи трансформации данных
- Найди потенциальные конфликты
```

## 🔄 Универсальные сценарии

### Безопасность и аудит

#### Анализ безопасности
```
Проведи аудит безопасности схемы FINANCE:
- Найди таблицы с чувствительными данными
- Проверь наличие ограничений
- Определи таблицы без логирования изменений
```

#### Соответствие стандартам
```
Проверь соответствие схемы PERSONAL_DATA требованиям GDPR:
- Найди таблицы с персональными данными
- Проверь возможность удаления данных
- Определи связанные таблицы
```

### Мониторинг и обслуживание

#### Анализ роста данных
```
Проанализируй таблицы логов и транзакций:
- Определи скорость роста данных
- Найди таблицы без партиционирования
- Предложи стратегию архивирования
```

#### Оптимизация хранения
```
Найди возможности оптимизации хранения:
- Таблицы с избыточными индексами
- Неиспользуемые столбцы
- Возможности сжатия данных
```

## 🎯 Специализированные запросы

### Для разработчиков

#### Создание ORM моделей
```
Создай модели Django для всех таблиц в схеме BLOG включая связи и валидацию
```

#### Генерация GraphQL схемы
```
На основе структуры таблиц USER, POST, COMMENT создай GraphQL схему с резолверами
```

### Для аналитиков данных

#### Подготовка данных для анализа
```
Подготовь структуру данных для анализа продаж:
- Определи факты и измерения
- Найди ключевые метрики в таблицах
- Предложи агрегации
```

#### Создание витрин данных
```
Спроектируй витрину данных для отчетности на основе таблиц SALES, CUSTOMERS, PRODUCTS
```

### Для администраторов БД

#### Мониторинг производительности
```
Найди таблицы требующие внимания:
- Без статистики
- С устаревшими индексами
- С большим количеством блокировок
```

#### Планирование обслуживания
```
Создай план обслуживания БД:
- Расписание обновления статистики
- Перестроение индексов
- Архивирование старых данных
```

## 🚀 Продвинутые техники

### Автоматизация

#### Создание скриптов мониторинга
```
Создай Python скрипт для мониторинга изменений в структуре схемы PRODUCTION
```

#### Генерация отчетов
```
Создай автоматический отчет о состоянии всех схем в базе данных
```

### Интеграция с CI/CD

#### Валидация схемы
```
Создай тесты для проверки целостности схемы перед деплоем
```

#### Автоматическая документация
```
Настрой автоматическое обновление документации БД при изменении структуры
```

## 💡 Советы по эффективному использованию

### Лучшие практики

1. **Начинайте с общего обзора**
   ```
   Покажи общую структуру базы данных - схемы, количество таблиц, основные связи
   ```

2. **Используйте контекст**
   ```
   В контексте интернет-магазина проанализируй таблицы заказов и предложи улучшения
   ```

3. **Комбинируйте инструменты**
   ```
   Сначала покажи структуру таблицы ORDERS, затем получи образец данных и проанализируй связи
   ```

4. **Задавайте конкретные вопросы**
   ```
   Какие индексы нужно добавить на таблицу TRANSACTIONS для ускорения поиска по дате и сумме?
   ```

### Типичные ошибки

❌ **Неправильно:** "Покажи все"
✅ **Правильно:** "Покажи структуру таблицы USERS в схеме APP"

❌ **Неправильно:** "Что не так с БД?"
✅ **Правильно:** "Найди таблицы без первичных ключей в схеме LEGACY"

❌ **Неправильно:** "Создай запрос"
✅ **Правильно:** "На основе структуры таблицы PRODUCTS создай запрос для поиска товаров по категории"

Используйте эти примеры как отправную точку для работы с Database Explorer MCP в вашем любимом ИИ-редакторе! 🎉
