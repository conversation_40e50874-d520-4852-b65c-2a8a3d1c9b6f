#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Скрипт для тестирования подключения к базе данных Oracle.
"""

import os
import sys
from loguru import logger
from dotenv import load_dotenv
import oracledb
from sqlalchemy import create_engine, text

# Настройка логирования
logger.add(
    "logs/connection_test.log",
    rotation="100 MB",
    retention="30 days",
    format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {function}:{line} | {message}",
    level="INFO"
)

def test_direct_connection():
    """Тестирование прямого подключения через oracledb."""
    load_dotenv()
    
    # Получение параметров подключения из переменных окружения
    oracle_user = os.environ.get("ORACLE_USER")
    oracle_pass = os.environ.get("ORACLE_PASS")
    oracle_host = os.environ.get("ORACLE_HOST")
    oracle_port = os.environ.get("ORACLE_PORT")
    oracle_base = os.environ.get("ORACLE_BASE")
    
    # Проверка наличия всех необходимых переменных
    required_vars = ["ORACLE_USER", "ORACLE_PASS", "ORACLE_HOST", "ORACLE_PORT", "ORACLE_BASE"]
    missing_vars = [var for var in required_vars if not os.environ.get(var)]
    
    if missing_vars:
        logger.error(f"Отсутствуют обязательные переменные окружения: {', '.join(missing_vars)}")
        return False
    
    try:
        # Создание DSN для подключения
        dsn = f"{oracle_host}:{oracle_port}/{oracle_base}"
        logger.info(f"Попытка прямого подключения к Oracle: {dsn}")
        
        # Подключение к базе данных
        connection = oracledb.connect(
            user=oracle_user,
            password=oracle_pass,
            dsn=dsn
        )
        
        # Выполнение простого запроса для проверки подключения
        cursor = connection.cursor()
        cursor.execute("SELECT 'Подключение успешно!' FROM DUAL")
        result = cursor.fetchone()
        
        logger.info(f"Результат запроса: {result[0]}")
        cursor.close()
        connection.close()
        
        logger.info("Прямое подключение к Oracle успешно установлено и закрыто")
        return True
    
    except Exception as e:
        logger.error(f"Ошибка при прямом подключении к Oracle: {str(e)}")
        return False

def test_sqlalchemy_connection():
    """Тестирование подключения через SQLAlchemy."""
    load_dotenv()
    
    # Получение параметров подключения из переменных окружения
    oracle_user = os.environ.get("ORACLE_USER")
    oracle_pass = os.environ.get("ORACLE_PASS")
    oracle_host = os.environ.get("ORACLE_HOST")
    oracle_port = os.environ.get("ORACLE_PORT")
    oracle_base = os.environ.get("ORACLE_BASE")
    
    # Проверка наличия всех необходимых переменных
    required_vars = ["ORACLE_USER", "ORACLE_PASS", "ORACLE_HOST", "ORACLE_PORT", "ORACLE_BASE"]
    missing_vars = [var for var in required_vars if not os.environ.get(var)]
    
    if missing_vars:
        logger.error(f"Отсутствуют обязательные переменные окружения: {', '.join(missing_vars)}")
        return False
    
    try:
        # Создание URL для подключения
        url = f"oracle+oracledb://{oracle_user}:{oracle_pass}@{oracle_host}:{oracle_port}/{oracle_base}"
        logger.info(f"Попытка подключения через SQLAlchemy к Oracle: {oracle_host}:{oracle_port}/{oracle_base}")
        
        # Создание движка SQLAlchemy
        engine = create_engine(url)
        
        # Выполнение простого запроса для проверки подключения
        with engine.connect() as connection:
            result = connection.execute(text("SELECT 'Подключение успешно!' FROM DUAL"))
            row = result.fetchone()
            logger.info(f"Результат запроса: {row[0]}")
        
        logger.info("Подключение через SQLAlchemy к Oracle успешно установлено и закрыто")
        return True
    
    except Exception as e:
        logger.error(f"Ошибка при подключении через SQLAlchemy к Oracle: {str(e)}")
        return False

def test_existing_config_connection():
    """Тестирование подключения через существующую конфигурацию."""
    try:
        logger.info("Попытка импорта существующей конфигурации подключения")
        
        # Импорт существующей конфигурации
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        from config_db import session, engine
        
        # Выполнение простого запроса для проверки подключения
        result = session.execute(text("SELECT 'Подключение успешно!' FROM DUAL"))
        row = result.fetchone()
        logger.info(f"Результат запроса: {row[0]}")
        
        logger.info("Подключение через существующую конфигурацию успешно установлено")
        return True
    
    except Exception as e:
        logger.error(f"Ошибка при подключении через существующую конфигурацию: {str(e)}")
        return False

if __name__ == "__main__":
    print("Тестирование подключения к Oracle...")
    
    # Создание директории для логов, если она не существует
    os.makedirs("logs", exist_ok=True)
    
    # Тестирование всех методов подключения
    direct_result = test_direct_connection()
    sqlalchemy_result = test_sqlalchemy_connection()
    config_result = test_existing_config_connection()
    
    # Вывод результатов
    print("\nРезультаты тестирования подключения к Oracle:")
    print(f"1. Прямое подключение через oracledb: {'УСПЕШНО' if direct_result else 'ОШИБКА'}")
    print(f"2. Подключение через SQLAlchemy: {'УСПЕШНО' if sqlalchemy_result else 'ОШИБКА'}")
    print(f"3. Подключение через существующую конфигурацию: {'УСПЕШНО' if config_result else 'ОШИБКА'}")
    
    if direct_result or sqlalchemy_result or config_result:
        print("\nПодключение к Oracle успешно установлено как минимум одним методом!")
        sys.exit(0)
    else:
        print("\nВсе методы подключения к Oracle завершились с ошибкой.")
        print("Проверьте настройки подключения и доступность базы данных.")
        sys.exit(1)
