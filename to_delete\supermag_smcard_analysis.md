# Анализ таблицы supermag.smcard

*Дата анализа: 2025-05-26 15:46:12*

## Основная информация


## Столбцы

| Имя | Тип данных | Nullable | По умолчанию |
|-----|------------|----------|-------------|
| article | VARCHAR(50) | NOT NULL |  |
| globalarticle | VARCHAR(50) | NULL |  |
| arrivedfrom | NUMBER | NULL |  |
| bornin | RAW | NOT NULL |  |
| name | VARCHAR(255) | NOT NULL |  |
| shortname | VARCHAR(255) | NULL |  |
| idmeasurement | NUMBER | NOT NULL |  |
| idclass | NUMBER | NOT NULL |  |
| idscale | NUMBER | NULL |  |
| subarticle | VARCHAR(255) | NULL |  |
| accepted | NUMBER | NOT NULL |  |
| datatype | NUMBER | NOT NULL |  |
| datasubtype | NUMBER | NOT NULL |  |
| scaleload | CHAR(1) | NOT NULL |  |
| cashload | CHAR(1) | NOT NULL |  |
| receiptok | CHAR(1) | NOT NULL |  |
| storage | NUMBER | NOT NULL |  |
| deadline | NUMBER | NULL |  |
| losses | NUMBER | NOT NULL |  |
| scrap | NUMBER | NOT NULL |  |
| waste | NUMBER | NOT NULL |  |
| mesname | VARCHAR(20) | NULL |  |
| mesabbrev | VARCHAR(6) | NULL |  |
| country | VARCHAR(255) | NULL |  |
| cardcomment | VARCHAR(255) | NULL |  |
| flags | NUMBER | NOT NULL |  |
| cutpricedays | NUMBER | NOT NULL |  |
| supplypricepercentp | NUMBER | NULL |  |
| supplypricepercentm | NUMBER | NULL |  |
| minprofit | NUMBER | NOT NULL |  |
| idthreetorg | NUMBER | NULL |  |
| idonetorg | NUMBER | NULL |  |
| idspiritcode | NUMBER | NULL |  |
| idmarketinggroup | NUMBER | NULL |  |
| idlossesgroup | NUMBER | NULL |  |
| quantitydeviation | NUMBER | NOT NULL |  |
| usetime | NUMBER | NULL |  |
| usetimedim | NUMBER | NOT NULL |  |
| weight | NUMBER | NULL |  |
| idmeasweight | NUMBER | NULL |  |
| width | NUMBER | NULL |  |
| length | NUMBER | NULL |  |
| height | NUMBER | NULL |  |
| idmeasdim | NUMBER | NULL |  |
| stateregulation | NUMBER | NULL |  |
| icing | NUMBER | NULL |  |
| altname1 | RAW | NULL |  |
| altname2 | RAW | NULL |  |
| nominalvalue | NUMBER | NULL |  |
| idcodetnved | NUMBER | NULL |  |
| proteins | NUMBER | NULL |  |
| fats | NUMBER | NULL |  |
| carbs | NUMBER | NULL |  |
| calories | NUMBER | NULL |  |
| idpersonalprotection | NUMBER | NULL |  |
| idokpd2 | NUMBER | NULL |  |

## Ограничения

### Первичный ключ

- **smcard_pk:** (article)

### Внешний ключ

- **smccard_arrivedfrom:** (arrivedfrom) -> supermag.
- **smccardclass:** (idclass) -> supermag.
- **smccard_codetnved:** (idcodetnved) -> supermag.
- **smccard_datatype:** (datatype) -> supermag.
- **smccard_global:** (globalarticle) -> supermag.
- **smccard_idlosses:** (idlossesgroup) -> supermag.
- **smccard_idmarketing:** (idmarketinggroup) -> supermag.
- **smccard_idokpd2:** (idokpd2) -> supermag.
- **smccard_idpersonalprotection:** (idpersonalprotection) -> supermag.
- **smccard_idspiritcode:** (idspiritcode) -> supermag.
- **smccardmeasdim:** (idmeasdim) -> supermag.
- **smccardmeasure:** (idmeasurement) -> supermag.
- **smccardmeasweight:** (idmeasweight) -> supermag.
- **smccard_onetorg:** (idonetorg) -> supermag.
- **smccard_scale:** (idscale) -> supermag.
- **smccard_storage:** (storage) -> supermag.
- **smccard_threetorg:** (idthreetorg) -> supermag.

## Индексы

| Имя | Тип | Уникальность | Столбцы |
|-----|-----|--------------|--------|
| smcard_arrivedfromidx | NORMAL | NONUNIQUE | arrivedfrom ASC |
| smcardclassifid | NORMAL | NONUNIQUE | idclass ASC |
| smcard_datatypeidx | NORMAL | NONUNIQUE | datatype ASC |
| smcard_globalartidx | NORMAL | NONUNIQUE | globalarticle ASC |
| smcardidcodetnved | NORMAL | NONUNIQUE | idcodetnved ASC |
| smcardidlossesgroup | NORMAL | NONUNIQUE | idlossesgroup ASC |
| smcardidmarketinggroup | NORMAL | NONUNIQUE | idmarketinggroup ASC |
| smcardidokpd2 | NORMAL | NONUNIQUE | idokpd2 ASC |
| smcardidonetorg | NORMAL | NONUNIQUE | idonetorg ASC |
| smcardidpersonalprotect | NORMAL | NONUNIQUE | idpersonalprotection ASC |
| smcardidspiritcode | NORMAL | NONUNIQUE | idspiritcode ASC |
| smcardidthreetorg | NORMAL | NONUNIQUE | idthreetorg ASC |
| smcard_measdimidx | NORMAL | NONUNIQUE | idmeasdim ASC |
| smcard_measurementidx | NORMAL | NONUNIQUE | idmeasurement ASC |
| smcard_measweightidx | NORMAL | NONUNIQUE | idmeasweight ASC |
| smcardscaleid | NORMAL | NONUNIQUE | idscale ASC |
| smcard_storageidx | NORMAL | NONUNIQUE | storage ASC |

## Зависимости

### Ссылается на

- **arrivedfrom** -> smpostlocations.id (через smccard_arrivedfrom)
- **idclass** -> sacardclass.id (через smccardclass)
- **idcodetnved** -> sacodestnved.id (через smccard_codetnved)
- **datatype** -> saarticletypes.id (через smccard_datatype)
- **globalarticle** -> smcard.article (через smccard_global)
- **idlossesgroup** -> salossesgroups.id (через smccard_idlosses)
- **idmarketinggroup** -> samarketinggroups.id (через smccard_idmarketing)
- **idokpd2** -> saokpd2.id (через smccard_idokpd2)
- **idpersonalprotection** -> sapersonalprotection.id (через smccard_idpersonalprotection)
- **idspiritcode** -> saspiritcode.id (через smccard_idspiritcode)
- **idmeasdim** -> sameasurement.id (через smccardmeasdim)
- **idmeasurement** -> sameasurement.id (через smccardmeasure)
- **idmeasweight** -> sameasurement.id (через smccardmeasweight)
- **idonetorg** -> saonetorg.id (через smccard_onetorg)
- **idscale** -> sascales.id (через smccard_scale)
- **storage** -> sastoragecond.id (через smccard_storage)
- **idthreetorg** -> sathreetorg.id (через smccard_threetorg)

