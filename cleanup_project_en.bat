@echo off
setlocal enabledelayedexpansion

:: =============================================================================
:: Database Explorer MCP - Project Cleanup Script
:: =============================================================================
:: This script safely moves unnecessary files to to_delete folder
:: Preserves all important MCP server components and documentation
:: =============================================================================

echo.
echo ================================================================================
echo                    Database Explorer MCP - Project Cleanup                    
echo ================================================================================
echo.

:: Check that script is run from project root
if not exist "mcp_server.py" (
    echo [ERROR] Script must be run from Database Explorer MCP project root
    echo         Make sure mcp_server.py file is in current directory
    echo.
    pause
    exit /b 1
)

echo [OK] Database Explorer MCP project found
echo.

:: Create to_delete folder
set "DELETE_DIR=to_delete"
if not exist "%DELETE_DIR%" (
    mkdir "%DELETE_DIR%"
    echo [CREATED] Folder: %DELETE_DIR%
) else (
    echo [EXISTS] Folder %DELETE_DIR% already exists
)
echo.

:: Counter for moved files
set /a MOVED_COUNT=0

echo [INFO] Starting project cleanup...
echo.

:: =============================================================================
:: 1. Move table analysis files (*.md except documentation)
:: =============================================================================
echo [STEP 1] Moving table analysis files...

for %%f in (ffmaprep_analysis.md supermag_*.md *_analysis.md) do (
    if exist "%%f" (
        move "%%f" "%DELETE_DIR%\" >nul 2>&1
        if !errorlevel! equ 0 (
            echo    [MOVED] %%f
            set /a MOVED_COUNT+=1
        )
    )
)

:: =============================================================================
:: 2. Move old configurations and prompts
:: =============================================================================
echo.
echo [STEP 2] Moving old configurations and prompts...

for %%f in (old_*.md *_prompt.md oracle_connection_prompt.md) do (
    if exist "%%f" (
        move "%%f" "%DELETE_DIR%\" >nul 2>&1
        if !errorlevel! equ 0 (
            echo    [MOVED] %%f
            set /a MOVED_COUNT+=1
        )
    )
)

:: =============================================================================
:: 3. Move test files (except test_mcp_server.py)
:: =============================================================================
echo.
echo [STEP 3] Moving test files...

for %%f in (my_test.py test_connection_pool.py test_drcp_connection.py test_oracle_connection.py query_*.py) do (
    if exist "%%f" (
        move "%%f" "%DELETE_DIR%\" >nul 2>&1
        if !errorlevel! equ 0 (
            echo    [MOVED] %%f
            set /a MOVED_COUNT+=1
        )
    )
)

:: =============================================================================
:: 4. Move Python cache
:: =============================================================================
echo.
echo [STEP 4] Moving Python cache...

if exist "__pycache__" (
    move "__pycache__" "%DELETE_DIR%\" >nul 2>&1
    if !errorlevel! equ 0 (
        echo    [MOVED] __pycache__ folder
        set /a MOVED_COUNT+=1
    )
)

:: Find and move __pycache__ in subfolders
for /d /r . %%d in (__pycache__) do (
    if exist "%%d" (
        set "rel_path=%%d"
        set "rel_path=!rel_path:%CD%\=!"
        if not "!rel_path!"=="!rel_path:to_delete=!" (
            rem Skip folders inside to_delete
        ) else (
            move "%%d" "%DELETE_DIR%\" >nul 2>&1
            if !errorlevel! equ 0 (
                echo    [MOVED] !rel_path!
                set /a MOVED_COUNT+=1
            )
        )
    )
)

:: Move .pyc and .pyo files
for /r . %%f in (*.pyc *.pyo) do (
    set "file_path=%%f"
    set "rel_path=!file_path:%CD%\=!"
    if not "!rel_path!"=="!rel_path:to_delete=!" (
        rem Skip files inside to_delete
    ) else (
        move "%%f" "%DELETE_DIR%\" >nul 2>&1
        if !errorlevel! equ 0 (
            echo    [MOVED] !rel_path!
            set /a MOVED_COUNT+=1
        )
    )
)

:: =============================================================================
:: 5. Move logs
:: =============================================================================
echo.
echo [STEP 5] Moving logs...

if exist "logs" (
    move "logs" "%DELETE_DIR%\" >nul 2>&1
    if !errorlevel! equ 0 (
        echo    [MOVED] logs folder
        set /a MOVED_COUNT+=1
    )
)

for %%f in (*.log) do (
    if exist "%%f" (
        move "%%f" "%DELETE_DIR%\" >nul 2>&1
        if !errorlevel! equ 0 (
            echo    [MOVED] %%f
            set /a MOVED_COUNT+=1
        )
    )
)

:: =============================================================================
:: 6. Move virtual environment
:: =============================================================================
echo.
echo [STEP 6] Moving virtual environment...

if exist "venv" (
    echo    [INFO] Moving venv folder (may take time)...
    move "venv" "%DELETE_DIR%\" >nul 2>&1
    if !errorlevel! equ 0 (
        echo    [MOVED] venv folder
        set /a MOVED_COUNT+=1
    ) else (
        echo    [WARNING] Could not move venv (may be in use)
    )
)

:: =============================================================================
:: 7. Move additional unnecessary files
:: =============================================================================
echo.
echo [STEP 7] Moving additional files...

for %%f in (_requirements.txt requirements_db_explorer.txt pdf_extractor.py) do (
    if exist "%%f" (
        move "%%f" "%DELETE_DIR%\" >nul 2>&1
        if !errorlevel! equ 0 (
            echo    [MOVED] %%f
            set /a MOVED_COUNT+=1
        )
    )
)

:: =============================================================================
:: Display results
:: =============================================================================
echo.
echo ================================================================================
echo                              CLEANUP RESULTS                             
echo ================================================================================
echo.

echo [STATS] Statistics:
echo    - Files/folders moved: !MOVED_COUNT!
echo    - Destination folder: %DELETE_DIR%
echo.

echo [PRESERVED] Important project files:
echo.
echo [MODULES] Main modules:
if exist "mcp_server.py" echo    [OK] mcp_server.py
if exist "db_explorer.py" echo    [OK] db_explorer.py
if exist "install_mcp.py" echo    [OK] install_mcp.py
if exist "test_mcp_server.py" echo    [OK] test_mcp_server.py
echo.

echo [FOLDERS] Code directories:
if exist "mcp_tools" echo    [OK] mcp_tools/
if exist "connectors" echo    [OK] connectors/
if exist "exporters" echo    [OK] exporters/
if exist "models" echo    [OK] models/
if exist "integrations" echo    [OK] integrations/
echo.

echo [CONFIG] Configuration:
if exist ".env.example" echo    [OK] .env.example
if exist "requirements.txt" echo    [OK] requirements.txt
if exist "requirements_mcp.txt" echo    [OK] requirements_mcp.txt
if exist "config_db.py" echo    [OK] config_db.py
echo.

echo [DOCS] Documentation:
if exist "README.md" echo    [OK] README.md
if exist "README_MCP.md" echo    [OK] README_MCP.md
if exist "QUICKSTART.md" echo    [OK] QUICKSTART.md
if exist "CLEANUP_GUIDE.md" echo    [OK] CLEANUP_GUIDE.md
echo.

if !MOVED_COUNT! gtr 0 (
    echo [SUCCESS] Project cleanup completed successfully!
    echo.
    echo [NEXT] What to do next:
    echo    1. Check contents of '%DELETE_DIR%' folder
    echo    2. If everything is OK, you can delete '%DELETE_DIR%' folder
    echo    3. Or keep it as backup copy
    echo.
    echo [READY] Project is ready to use:
    echo    - python test_mcp_server.py  - test MCP server
    echo    - python install_mcp.py     - install to Claude Desktop
    echo    - python mcp_server.py      - run MCP server
) else (
    echo [INFO] No files found to move - project already clean!
)

echo.
echo ================================================================================
echo                                 COMPLETED                                   
echo ================================================================================
echo.

pause
