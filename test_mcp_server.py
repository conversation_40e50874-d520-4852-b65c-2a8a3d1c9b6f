#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Скрипт для тестирования MCP сервера Database Explorer.
"""

import asyncio
import json
from typing import Dict, Any
from mcp.client.session import ClientSession
from mcp.client.stdio import StdioServerParameters, stdio_client


async def test_mcp_server():
    """
    Тестирует функциональность MCP сервера.
    """
    print("🚀 Запуск тестирования MCP сервера Database Explorer")
    
    # Параметры для подключения к серверу
    server_params = StdioServerParameters(
        command="python",
        args=["mcp_server.py"]
    )
    
    try:
        async with stdio_client(server_params) as (read, write):
            async with ClientSession(read, write) as session:
                print("✅ Подключение к MCP серверу установлено")
                
                # Инициализация сессии
                await session.initialize()
                print("✅ Сессия инициализирована")
                
                # Получение списка доступных инструментов
                print("\n📋 Получение списка доступных инструментов...")
                tools = await session.list_tools()
                
                print(f"Найдено {len(tools.tools)} инструментов:")
                for tool in tools.tools:
                    print(f"  - {tool.name}: {tool.description}")
                
                # Тест 1: Проверка статуса подключения
                print("\n🔍 Тест 1: Проверка статуса подключения")
                try:
                    result = await session.call_tool("get_connection_status", {})
                    print(f"Результат: {json.dumps(result.content[0].text, indent=2, ensure_ascii=False)}")
                except Exception as e:
                    print(f"❌ Ошибка: {e}")
                
                # Тест 2: Подключение к базе данных (если настроены переменные окружения)
                print("\n🔍 Тест 2: Попытка подключения к базе данных")
                try:
                    result = await session.call_tool("connect_database", {
                        "db_type": "oracle"
                    })
                    connection_result = json.loads(result.content[0].text)
                    print(f"Результат подключения: {json.dumps(connection_result, indent=2, ensure_ascii=False)}")
                    
                    if connection_result.get("success"):
                        print("✅ Подключение к базе данных успешно!")
                        
                        # Тест 3: Получение списка схем
                        print("\n🔍 Тест 3: Получение списка схем")
                        try:
                            result = await session.call_tool("list_schemas", {})
                            schemas_result = json.loads(result.content[0].text)
                            print(f"Схемы: {json.dumps(schemas_result, indent=2, ensure_ascii=False)}")
                        except Exception as e:
                            print(f"❌ Ошибка при получении схем: {e}")
                        
                        # Тест 4: Получение информации о текущей схеме
                        print("\n🔍 Тест 4: Получение информации о текущей схеме")
                        try:
                            result = await session.call_tool("get_schema_info", {})
                            schema_info = json.loads(result.content[0].text)
                            print(f"Информация о схеме: {json.dumps(schema_info, indent=2, ensure_ascii=False)}")
                        except Exception as e:
                            print(f"❌ Ошибка при получении информации о схеме: {e}")
                        
                        # Тест 5: Получение списка таблиц
                        print("\n🔍 Тест 5: Получение списка таблиц в текущей схеме")
                        try:
                            result = await session.call_tool("list_tables_in_schema", {})
                            tables_result = json.loads(result.content[0].text)
                            print(f"Таблицы: {json.dumps(tables_result, indent=2, ensure_ascii=False)}")
                            
                            # Если есть таблицы, тестируем одну из них
                            if tables_result.get("success") and tables_result.get("tables"):
                                first_table = tables_result["tables"][0]["table_name"]
                                schema_name = tables_result["schema_name"]
                                
                                # Тест 6: Получение структуры таблицы
                                print(f"\n🔍 Тест 6: Получение структуры таблицы {first_table}")
                                try:
                                    result = await session.call_tool("get_table_structure", {
                                        "table_name": first_table,
                                        "schema_name": schema_name
                                    })
                                    structure_result = json.loads(result.content[0].text)
                                    print(f"Структура таблицы: {json.dumps(structure_result, indent=2, ensure_ascii=False)}")
                                except Exception as e:
                                    print(f"❌ Ошибка при получении структуры таблицы: {e}")
                                
                                # Тест 7: Получение образца данных
                                print(f"\n🔍 Тест 7: Получение образца данных из таблицы {first_table}")
                                try:
                                    result = await session.call_tool("get_table_sample_data", {
                                        "table_name": first_table,
                                        "schema_name": schema_name,
                                        "limit": 5
                                    })
                                    sample_result = json.loads(result.content[0].text)
                                    print(f"Образец данных: {json.dumps(sample_result, indent=2, ensure_ascii=False)}")
                                except Exception as e:
                                    print(f"❌ Ошибка при получении образца данных: {e}")
                        
                        except Exception as e:
                            print(f"❌ Ошибка при получении списка таблиц: {e}")
                        
                        # Тест 8: Выполнение безопасного запроса
                        print("\n🔍 Тест 8: Выполнение безопасного запроса")
                        try:
                            result = await session.call_tool("execute_safe_query", {
                                "query": "SELECT 1 FROM DUAL",
                                "limit": 1
                            })
                            query_result = json.loads(result.content[0].text)
                            print(f"Результат запроса: {json.dumps(query_result, indent=2, ensure_ascii=False)}")
                        except Exception as e:
                            print(f"❌ Ошибка при выполнении запроса: {e}")
                        
                        # Отключение от базы данных
                        print("\n🔍 Отключение от базы данных")
                        try:
                            result = await session.call_tool("disconnect_database", {})
                            disconnect_result = json.loads(result.content[0].text)
                            print(f"Результат отключения: {json.dumps(disconnect_result, indent=2, ensure_ascii=False)}")
                        except Exception as e:
                            print(f"❌ Ошибка при отключении: {e}")
                    
                    else:
                        print("❌ Не удалось подключиться к базе данных")
                        print("💡 Убедитесь, что настроены переменные окружения или config_db.py")
                
                except Exception as e:
                    print(f"❌ Ошибка при подключении к базе данных: {e}")
                
                print("\n✅ Тестирование завершено!")
                
    except Exception as e:
        print(f"❌ Ошибка при подключении к MCP серверу: {e}")
        print("💡 Убедитесь, что mcp_server.py запускается без ошибок")


async def test_tools_list():
    """
    Тестирует получение списка инструментов без подключения к БД.
    """
    print("🔧 Тестирование списка инструментов MCP сервера")
    
    server_params = StdioServerParameters(
        command="python",
        args=["mcp_server.py"]
    )
    
    try:
        async with stdio_client(server_params) as (read, write):
            async with ClientSession(read, write) as session:
                await session.initialize()
                
                # Получение списка инструментов
                tools = await session.list_tools()
                
                print(f"\n📋 Доступно {len(tools.tools)} инструментов:")
                print("=" * 80)
                
                for i, tool in enumerate(tools.tools, 1):
                    print(f"{i:2d}. {tool.name}")
                    print(f"    Описание: {tool.description}")
                    
                    if hasattr(tool, 'inputSchema') and tool.inputSchema:
                        properties = tool.inputSchema.get('properties', {})
                        if properties:
                            print("    Параметры:")
                            for param_name, param_info in properties.items():
                                param_type = param_info.get('type', 'unknown')
                                param_desc = param_info.get('description', 'Нет описания')
                                required = param_name in tool.inputSchema.get('required', [])
                                req_mark = " (обязательный)" if required else " (опциональный)"
                                print(f"      - {param_name} ({param_type}){req_mark}: {param_desc}")
                    print()
                
                print("=" * 80)
                print("✅ Список инструментов получен успешно!")
                
    except Exception as e:
        print(f"❌ Ошибка: {e}")


if __name__ == "__main__":
    print("Database Explorer MCP Server - Тестирование")
    print("=" * 50)
    
    # Выбор типа тестирования
    print("Выберите тип тестирования:")
    print("1. Полное тестирование (с подключением к БД)")
    print("2. Тестирование списка инструментов")
    
    choice = input("Введите номер (1 или 2): ").strip()
    
    if choice == "1":
        asyncio.run(test_mcp_server())
    elif choice == "2":
        asyncio.run(test_tools_list())
    else:
        print("❌ Неверный выбор. Запуск тестирования списка инструментов...")
        asyncio.run(test_tools_list())
