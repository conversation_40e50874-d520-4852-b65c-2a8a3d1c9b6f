#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Скрипт для тестирования подключения к базе данных Oracle с использованием DRCP.
"""

import os
import sys
import time
import concurrent.futures
from loguru import logger
from sqlalchemy import text

# Настройка логирования
logger.add(
    "logs/drcp_test.log",
    rotation="100 MB",
    retention="30 days",
    format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {function}:{line} | {message}",
    level="INFO"
)

# Создание директории для логов, если она не существует
os.makedirs("logs", exist_ok=True)

def get_drcp_status():
    """Получение статуса DRCP из базы данных."""
    try:
        # Импорт существующей конфигурации
        from config_db import session
        
        # Запрос статуса DRCP
        result = session.execute(text("""
            SELECT 
                cp.status,
                cp.minsize,
                cp.maxsize,
                cp.incrsize,
                cp.inactivity_timeout,
                cp.max_think_time
            FROM DBA_CPOOL_INFO cp
        """))
        
        row = result.fetchone()
        if row:
            return {
                "status": row.status,
                "minsize": row.minsize,
                "maxsize": row.maxsize,
                "incrsize": row.incrsize,
                "inactivity_timeout": row.inactivity_timeout,
                "max_think_time": row.max_think_time
            }
        else:
            return {"error": "Информация о DRCP не найдена"}
    except Exception as e:
        logger.error(f"Ошибка при получении статуса DRCP: {str(e)}")
        return {"error": str(e)}

def get_connection_info():
    """Получение информации о текущем соединении."""
    try:
        # Импорт существующей конфигурации
        from config_db import session
        
        # Запрос информации о соединении
        result = session.execute(text("""
            SELECT 
                sys_context('USERENV', 'SESSION_USER') as username,
                sys_context('USERENV', 'SID') as session_id,
                sys_context('USERENV', 'HOST') as hostname,
                sys_context('USERENV', 'SERVER_HOST') as server_hostname,
                sys_context('USERENV', 'INSTANCE_NAME') as instance_name,
                sys_context('USERENV', 'SERVICE_NAME') as service_name
            FROM DUAL
        """))
        
        row = result.fetchone()
        return {
            "username": row.username,
            "session_id": row.session_id,
            "hostname": row.hostname,
            "server_hostname": row.server_hostname,
            "instance_name": row.instance_name,
            "service_name": row.service_name
        }
    except Exception as e:
        logger.error(f"Ошибка при получении информации о соединении: {str(e)}")
        return {"error": str(e)}

def execute_query(query_id):
    """Выполнение запроса к базе данных."""
    try:
        # Импорт существующей конфигурации
        from config_db import session
        
        start_time = time.time()
        
        # Выполнение простого запроса
        result = session.execute(text("SELECT 'Запрос выполнен успешно!' FROM DUAL"))
        row = result.fetchone()
        
        # Получение информации о соединении
        connection_info = session.execute(text("""
            SELECT 
                sys_context('USERENV', 'SESSION_USER') as username,
                sys_context('USERENV', 'SID') as session_id,
                sys_context('USERENV', 'HOST') as hostname
            FROM DUAL
        """)).fetchone()
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        logger.info(f"Запрос {query_id}: Выполнен за {execution_time:.4f} сек. "
                   f"Сессия: {connection_info.session_id}, "
                   f"Пользователь: {connection_info.username}")
        
        return {
            "query_id": query_id,
            "execution_time": execution_time,
            "session_id": connection_info.session_id,
            "username": connection_info.username,
            "hostname": connection_info.hostname,
            "result": row[0]
        }
    except Exception as e:
        logger.error(f"Ошибка при выполнении запроса {query_id}: {str(e)}")
        return {
            "query_id": query_id,
            "error": str(e)
        }

def test_concurrent_queries(num_queries=20, max_workers=10):
    """Тестирование параллельных запросов с использованием пула потоков."""
    logger.info(f"Начало тестирования {num_queries} параллельных запросов "
               f"с {max_workers} рабочими потоками...")
    
    start_time = time.time()
    results = []
    
    with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
        # Создание списка задач
        futures = [executor.submit(execute_query, f"DRCP-{i+1}") for i in range(num_queries)]
        
        # Получение результатов по мере их завершения
        for future in concurrent.futures.as_completed(futures):
            results.append(future.result())
    
    end_time = time.time()
    total_time = end_time - start_time
    
    logger.info(f"Завершено тестирование параллельных запросов с DRCP. "
               f"Общее время: {total_time:.4f} сек. "
               f"Среднее время на запрос: {total_time/num_queries:.4f} сек.")
    
    return results, total_time

def analyze_results(results_data, total_time):
    """Анализ результатов тестирования."""
    num_queries = len(results_data)
    
    # Расчет средних значений
    avg_time = total_time / num_queries if num_queries > 0 else 0
    
    # Сбор уникальных сессий
    unique_sessions = set(item.get("session_id") for item in results_data if "session_id" in item)
    
    # Расчет статистики по времени выполнения
    execution_times = [item.get("execution_time", 0) for item in results_data if "execution_time" in item]
    min_time = min(execution_times) if execution_times else 0
    max_time = max(execution_times) if execution_times else 0
    
    print("\nАнализ результатов тестирования DRCP:")
    print(f"  Параллельные запросы ({num_queries}):")
    print(f"    Общее время: {total_time:.4f} сек.")
    print(f"    Среднее время на запрос: {avg_time:.4f} сек.")
    print(f"    Минимальное время запроса: {min_time:.4f} сек.")
    print(f"    Максимальное время запроса: {max_time:.4f} сек.")
    print(f"    Количество уникальных сессий: {len(unique_sessions)}")
    print(f"    Уникальные сессии: {', '.join(sorted(unique_sessions))}")
    
    return {
        "total_queries": num_queries,
        "total_time": total_time,
        "avg_time": avg_time,
        "min_time": min_time,
        "max_time": max_time,
        "unique_sessions": len(unique_sessions),
        "session_ids": sorted(list(unique_sessions))
    }

if __name__ == "__main__":
    print("Тестирование подключения к Oracle с использованием DRCP...")
    
    # Получение статуса DRCP
    drcp_status = get_drcp_status()
    print("\nСтатус DRCP на сервере Oracle:")
    for key, value in drcp_status.items():
        print(f"  {key}: {value}")
    
    # Проверка, активен ли DRCP
    if "status" in drcp_status and drcp_status["status"] != "ACTIVE":
        print("\nВНИМАНИЕ: DRCP не активен на сервере Oracle!")
        print("Для активации DRCP выполните следующую команду на сервере Oracle:")
        print("  EXECUTE DBMS_CONNECTION_POOL.START_POOL();")
        print("\nПродолжаем тестирование, но DRCP не будет использоваться...")
    
    # Получение информации о соединении
    conn_info = get_connection_info()
    print("\nИнформация о текущем соединении:")
    for key, value in conn_info.items():
        print(f"  {key}: {value}")
    
    # Количество запросов для тестирования
    num_queries = 20
    max_workers = 10
    
    # Тестирование параллельных запросов
    print(f"\nВыполнение {num_queries} параллельных запросов с использованием DRCP...")
    results, total_time = test_concurrent_queries(num_queries, max_workers)
    
    # Анализ результатов
    analysis = analyze_results(results, total_time)
    
    print("\nТестирование DRCP завершено!")
    print(f"Подробные логи доступны в файле: logs/drcp_test.log")
