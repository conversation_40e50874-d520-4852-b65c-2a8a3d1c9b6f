@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

:: =============================================================================
:: Database Explorer MCP - Скрипт восстановления проекта
:: =============================================================================
:: Этот скрипт восстанавливает файлы из папки to_delete обратно в проект
:: Используется в случае, если нужно отменить очистку
:: =============================================================================

echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                Database Explorer MCP - Восстановление проекта                ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.

:: Проверка наличия папки to_delete
set "DELETE_DIR=to_delete"
if not exist "%DELETE_DIR%" (
    echo ❌ ОШИБКА: Папка %DELETE_DIR% не найдена
    echo    Нечего восстанавливать
    echo.
    pause
    exit /b 1
)

echo ✅ Папка %DELETE_DIR% найдена
echo.

:: Подсчет файлов для восстановления
set /a FILE_COUNT=0
for /r "%DELETE_DIR%" %%f in (*.*) do (
    set /a FILE_COUNT+=1
)

for /d "%DELETE_DIR%\*" %%d in (*) do (
    set /a FILE_COUNT+=1
)

if !FILE_COUNT! equ 0 (
    echo ℹ️  Папка %DELETE_DIR% пуста - нечего восстанавливать
    echo.
    pause
    exit /b 0
)

echo 📊 Найдено файлов/папок для восстановления: !FILE_COUNT!
echo.

:: Запрос подтверждения
echo ⚠️  ВНИМАНИЕ: Восстановление переместит все файлы из %DELETE_DIR% обратно в проект
echo    Это может перезаписать существующие файлы!
echo.
set /p "CONFIRM=Продолжить восстановление? (y/N): "

if /i not "!CONFIRM!"=="y" if /i not "!CONFIRM!"=="yes" (
    echo ❌ Восстановление отменено пользователем
    echo.
    pause
    exit /b 0
)

echo.
echo 🔄 Начинаем восстановление...
echo.

:: Счетчик восстановленных файлов
set /a RESTORED_COUNT=0

:: =============================================================================
:: Восстановление файлов из корня to_delete
:: =============================================================================
echo 📁 Восстановление файлов из корня %DELETE_DIR%...

for %%f in ("%DELETE_DIR%\*.*") do (
    if exist "%%f" (
        set "filename=%%~nxf"
        move "%%f" "." >nul 2>&1
        if !errorlevel! equ 0 (
            echo    ✓ Восстановлен: !filename!
            set /a RESTORED_COUNT+=1
        ) else (
            echo    ❌ Ошибка восстановления: !filename!
        )
    )
)

:: =============================================================================
:: Восстановление папок из to_delete
:: =============================================================================
echo.
echo 📂 Восстановление папок из %DELETE_DIR%...

for /d %%d in ("%DELETE_DIR%\*") do (
    set "dirname=%%~nxd"
    if exist "%%d" (
        if exist "!dirname!" (
            echo    ⚠️  Папка !dirname! уже существует - пропускаем
        ) else (
            move "%%d" "." >nul 2>&1
            if !errorlevel! equ 0 (
                echo    ✓ Восстановлена папка: !dirname!
                set /a RESTORED_COUNT+=1
            ) else (
                echo    ❌ Ошибка восстановления папки: !dirname!
            )
        )
    )
)

:: =============================================================================
:: Проверка и удаление пустой папки to_delete
:: =============================================================================
echo.
echo 🧹 Проверка папки %DELETE_DIR%...

:: Подсчет оставшихся файлов
set /a REMAINING_COUNT=0
for /r "%DELETE_DIR%" %%f in (*.*) do (
    set /a REMAINING_COUNT+=1
)

if !REMAINING_COUNT! equ 0 (
    rmdir "%DELETE_DIR%" >nul 2>&1
    if !errorlevel! equ 0 (
        echo    ✓ Пустая папка %DELETE_DIR% удалена
    )
) else (
    echo    ℹ️  В папке %DELETE_DIR% остались файлы (!REMAINING_COUNT!)
)

:: =============================================================================
:: Вывод результатов
:: =============================================================================
echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                            РЕЗУЛЬТАТЫ ВОССТАНОВЛЕНИЯ                        ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.

echo 📊 Статистика:
echo    • Восстановлено файлов/папок: !RESTORED_COUNT!
echo    • Найдено для восстановления: !FILE_COUNT!
echo.

if !RESTORED_COUNT! gtr 0 (
    echo 🎉 Восстановление завершено!
    echo.
    echo 💡 Рекомендации:
    echo    1. Проверьте, что все нужные файлы восстановлены
    echo    2. Запустите тестирование: python test_mcp_server.py
    echo    3. При необходимости повторите очистку: cleanup_project.bat
) else (
    echo ⚠️  Файлы не были восстановлены
    echo    Возможные причины:
    echo    • Файлы уже существуют в проекте
    echo    • Ошибки доступа к файлам
    echo    • Папка %DELETE_DIR% была пуста
)

echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                                 ЗАВЕРШЕНО                                   ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.

pause
