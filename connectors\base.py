#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Базовый абстрактный класс для коннекторов к различным СУБД.
"""

from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional, Tuple


class DatabaseConnector(ABC):
    """
    Абстрактный базовый класс для коннекторов к различным СУБД.
    
    Определяет общий интерфейс для всех коннекторов, который должен быть реализован
    в конкретных классах для каждой СУБД.
    """
    
    @abstractmethod
    def connect(self) -> bool:
        """
        Устанавливает соединение с базой данных.
        
        Returns:
            bool: True, если соединение успешно установлено, иначе False.
        """
        pass
    
    @abstractmethod
    def disconnect(self) -> None:
        """
        Закрывает соединение с базой данных.
        """
        pass
    
    @abstractmethod
    def execute_query(self, query: str, params: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """
        Выполняет SQL-запрос и возвращает результат.
        
        Args:
            query: SQL-запрос.
            params: Параметры запроса.
            
        Returns:
            List[Dict[str, Any]]: Результат запроса в виде списка словарей.
        """
        pass
    
    @abstractmethod
    def get_schema_names(self) -> List[str]:
        """
        Получает список доступных схем в базе данных.
        
        Returns:
            List[str]: Список имен схем.
        """
        pass
    
    @abstractmethod
    def get_table_names(self, schema_name: Optional[str] = None) -> List[str]:
        """
        Получает список таблиц в указанной схеме.
        
        Args:
            schema_name: Имя схемы. Если None, используется текущая/дефолтная схема.
            
        Returns:
            List[str]: Список имен таблиц.
        """
        pass
    
    @abstractmethod
    def get_table_info(self, table_name: str, schema_name: Optional[str] = None) -> Dict[str, Any]:
        """
        Получает информацию о таблице.
        
        Args:
            table_name: Имя таблицы.
            schema_name: Имя схемы. Если None, используется текущая/дефолтная схема.
            
        Returns:
            Dict[str, Any]: Информация о таблице.
        """
        pass
    
    @abstractmethod
    def get_column_info(self, table_name: str, schema_name: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Получает информацию о столбцах таблицы.
        
        Args:
            table_name: Имя таблицы.
            schema_name: Имя схемы. Если None, используется текущая/дефолтная схема.
            
        Returns:
            List[Dict[str, Any]]: Список словарей с информацией о столбцах.
        """
        pass
    
    @abstractmethod
    def get_constraints_info(self, table_name: str, schema_name: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Получает информацию об ограничениях таблицы.
        
        Args:
            table_name: Имя таблицы.
            schema_name: Имя схемы. Если None, используется текущая/дефолтная схема.
            
        Returns:
            List[Dict[str, Any]]: Список словарей с информацией об ограничениях.
        """
        pass
    
    @abstractmethod
    def get_indexes_info(self, table_name: str, schema_name: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Получает информацию об индексах таблицы.
        
        Args:
            table_name: Имя таблицы.
            schema_name: Имя схемы. Если None, используется текущая/дефолтная схема.
            
        Returns:
            List[Dict[str, Any]]: Список словарей с информацией об индексах.
        """
        pass
    
    @abstractmethod
    def get_dependencies_info(self, table_name: str, schema_name: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Получает информацию о зависимостях таблицы (внешние ключи).
        
        Args:
            table_name: Имя таблицы.
            schema_name: Имя схемы. Если None, используется текущая/дефолтная схема.
            
        Returns:
            List[Dict[str, Any]]: Список словарей с информацией о зависимостях.
        """
        pass
    
    @abstractmethod
    def get_current_schema(self) -> str:
        """
        Получает имя текущей схемы.
        
        Returns:
            str: Имя текущей схемы.
        """
        pass
